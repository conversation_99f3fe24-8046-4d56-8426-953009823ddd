import { Home, BookOpen, UserCircle, LayoutDashboard, Info, Mail, GraduationCap, Settings } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { useAdminStatus } from "@/hooks/use-admin-status";
import { cn } from "@/lib/utils";

interface DesktopSideNavProps {
  className?: string;
}

const DesktopSideNav = ({ className }: DesktopSideNavProps) => {
  const location = useLocation();
  const path = location.pathname;
  const { user } = useAuth();
  const { isAdmin } = useAdminStatus(user);

  // Define navigation items
  const navItems = [
    {
      name: "Home",
      path: "/",
      icon: Home,
      active: path === "/",
    },
    {
      name: "Domains",
      path: "/domains",
      icon: GraduationCap,
      active: path === "/domains" || path.startsWith("/domains/"),
    },
    {
      name: "Learn",
      path: "/learn",
      icon: BookOpen,
      active: path === "/learn" || path.startsWith("/learn/"),
    },
    {
      name: "Quizzes",
      path: "/quizzes",
      icon: BookOpen,
      active: path === "/quizzes",
    },
    {
      name: "Profile",
      path: "/profile",
      icon: UserCircle,
      active: path === "/profile",
      requiresAuth: true
    },
    {
      name: "About",
      path: "/about",
      icon: Info,
      active: path === "/about",
    },
    {
      name: "Contact",
      path: "/contact",
      icon: Mail,
      active: path === "/contact",
    }
  ];

  // Add admin and developer items if user is admin
  const isDevUser = user && (user.email || '').toLowerCase() === '<EMAIL>';
  const allItems = isAdmin
    ? [
        ...navItems,
        {
          name: "Admin",
          path: "/admin",
          icon: LayoutDashboard,
          active: path === "/admin",
          requiresAuth: true,
          requiresAdmin: true
        },
        {
          name: "Admin Domains",
          path: "/admin/domains",
          icon: Settings,
          active: path === "/admin/domains",
          requiresAuth: true,
          requiresAdmin: true
        },
        ...((process.env.NODE_ENV === 'development' || isDevUser) ? [{
          name: "Developer",
          path: "/developer",
          icon: LayoutDashboard,
          active: path === "/developer",
          requiresAuth: true,
          requiresAdmin: true
        }] : [])
      ]
    : navItems;

  return (
    <div className={cn(
      "hidden md:flex flex-col gap-2 p-4 border-r h-screen sticky top-0 bg-background/95 backdrop-blur-sm w-64 shadow-sm overflow-y-auto",
      className
    )}>
      {/* Logo */}
      <Link
        to="/"
        className="flex items-center gap-2 mb-8 transition-transform hover:scale-105 px-4 py-2"
      >
        <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-8 w-8" />
        <span className="font-bold text-xl bg-gradient-to-r from-cyber-primary to-cyber-accent bg-clip-text text-transparent">
          SecQuiz
        </span>
      </Link>

      {/* Navigation Links */}
      <div className="flex flex-col gap-6 mt-4">
        {/* Main Navigation */}
        <div className="flex flex-col gap-1">
          <div className="px-4 mb-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">Main</div>
          {allItems.filter(item => ['Home', 'Domains', 'Learn', 'Quizzes'].includes(item.name)).map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "flex items-center gap-3 px-4 py-3 rounded-md transition-all duration-200 relative",
                item.active
                  ? "bg-cyber-primary/10 text-cyber-primary font-medium"
                  : "text-muted-foreground hover:bg-cyber-primary/5 hover:text-foreground"
              )}
            >
              {item.active && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-cyber-primary rounded-full" />
              )}
              <item.icon className={cn("h-5 w-5", item.active && "text-cyber-primary")} />
              <span>{item.name}</span>
            </Link>
          ))}
        </div>

        {/* User Section */}
        <div className="flex flex-col gap-1">
          <div className="px-4 mb-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">Admin</div>
          {allItems.filter(item =>
            ['Profile'].includes(item.name) ||
            (['Admin', 'Admin Domains'].includes(item.name) && isAdmin)
          ).map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "flex items-center gap-3 px-4 py-3 rounded-md transition-all duration-200 relative",
                item.active
                  ? "bg-cyber-primary/10 text-cyber-primary font-medium"
                  : "text-muted-foreground hover:bg-cyber-primary/5 hover:text-foreground"
              )}
            >
              {item.active && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-cyber-primary rounded-full" />
              )}
              <item.icon className={cn("h-5 w-5", item.active && "text-cyber-primary")} />
              <span>{item.name}</span>
            </Link>
          ))}
        </div>

        {/* Info Section */}
        <div className="flex flex-col gap-1">
          <div className="px-4 mb-1 text-xs font-medium text-muted-foreground uppercase tracking-wider">Info</div>
          {allItems.filter(item => ['About', 'Contact'].includes(item.name)).map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "flex items-center gap-3 px-4 py-3 rounded-md transition-all duration-200 relative",
                item.active
                  ? "bg-cyber-primary/10 text-cyber-primary font-medium"
                  : "text-muted-foreground hover:bg-cyber-primary/5 hover:text-foreground"
              )}
            >
              {item.active && (
                <div className="absolute left-0 top-0 bottom-0 w-1 bg-cyber-primary rounded-full" />
              )}
              <item.icon className={cn("h-5 w-5", item.active && "text-cyber-primary")} />
              <span>{item.name}</span>
            </Link>
          ))}
        </div>
      </div>

      <div className="mt-auto mb-4 px-4 py-3 text-xs text-muted-foreground">
        <p className="opacity-70">© 2024 SecQuiz</p>
        <p className="opacity-70">Cybersecurity Education</p>
      </div>
    </div>
  );
};

export default DesktopSideNav;
