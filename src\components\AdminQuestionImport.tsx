
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { AlertCircle, FileText, Upload } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Topic } from "@/hooks/use-admin";

type AdminQuestionImportProps = {
  topics: Topic[];
  onSuccess: () => void;
  onCancel: () => void;
};

const AdminQuestionImport = ({ topics, onSuccess, onCancel }: AdminQuestionImportProps) => {
  const { toast } = useToast();
  const [importText, setImportText] = useState("");
  const [topicId, setTopicId] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setValidationErrors([]);
    setSuccessMessage("");

    // Validation
    const errors: string[] = [];

    if (!topicId) {
      errors.push("Please select a topic");
    }

    if (!importText.trim()) {
      errors.push("Please enter questions to import");
    }

    if (importText.trim().length < 50) {
      errors.push("Import text seems too short. Please ensure you have complete questions.");
    }

    if (!importText.includes("Q:")) {
      errors.push("No questions found. Questions should start with 'Q:'");
    }

    if (!importText.includes("Answer:")) {
      errors.push("No answers found. Each question should have an 'Answer:' line");
    }

    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Parse questions in the format:
      // Q: Question text
      // A: Option 1
      // B: Option 2
      // C: Option 3
      // D: Option 4
      // Answer: A
      // Explanation: Explanation text
      // ---
      const questions = [];
      const questionBlocks = importText.split("---").filter(block => block.trim());

      interface ImportedQuestion {
        topic_id: string;
        options: Record<string, string>;
        correct_answer: string;
        question_text?: string;
        explanation?: string;
      }

      for (const block of questionBlocks) {
        const lines = block.trim().split("\n").map(line => line.trim()).filter(line => line.length > 0);
        const questionObj: ImportedQuestion = {
          topic_id: topicId,
          options: {},
          correct_answer: "",
        };

        for (const line of lines) {
          const trimmedLine = line.trim();

          if (trimmedLine.startsWith("Q:")) {
            questionObj.question_text = trimmedLine.substring(2).trim();
          } else if (trimmedLine.match(/^[A-D]:/)) {
            const optionKey = trimmedLine.substring(0, 1);
            const optionValue = trimmedLine.substring(2).trim();
            if (optionValue) {
              questionObj.options[optionKey] = optionValue;
            }
          } else if (trimmedLine.startsWith("Answer:")) {
            const answer = trimmedLine.substring(7).trim().toUpperCase();
            // Enhanced validation: Convert numeric format to letter format and validate
            let standardizedAnswer = answer;

            // Convert legacy numeric format to standardized letter format
            if (answer === '0') standardizedAnswer = 'A';
            else if (answer === '1') standardizedAnswer = 'B';
            else if (answer === '2') standardizedAnswer = 'C';
            else if (answer === '3') standardizedAnswer = 'D';

            // Validate answer is A, B, C, or D (standardized format)
            if (['A', 'B', 'C', 'D'].includes(standardizedAnswer)) {
              questionObj.correct_answer = standardizedAnswer;

              // Log conversion if it occurred
              if (answer !== standardizedAnswer) {
                console.log(`Converted answer format: "${answer}" → "${standardizedAnswer}"`);
              }
            } else {
              console.warn(`Invalid answer format: "${answer}". Expected A, B, C, D or 0, 1, 2, 3`);
            }
          } else if (trimmedLine.startsWith("Explanation:")) {
            questionObj.explanation = trimmedLine.substring(12).trim();
          }
        }

        // Validate question has all required fields
        if (
          questionObj.question_text &&
          questionObj.question_text.length >= 10 &&
          Object.keys(questionObj.options).length >= 2 &&
          questionObj.correct_answer &&
          questionObj.options[questionObj.correct_answer] // Ensure the correct answer exists as an option
        ) {
          questions.push(questionObj);
        } else {
          console.warn("Skipping invalid question:", {
            hasQuestionText: !!questionObj.question_text,
            questionTextLength: questionObj.question_text?.length || 0,
            optionCount: Object.keys(questionObj.options).length,
            hasCorrectAnswer: !!questionObj.correct_answer,
            correctAnswerExists: questionObj.correct_answer ? !!questionObj.options[questionObj.correct_answer] : false,
            questionObj
          });
        }
      }
      if (questions.length === 0) {
        throw new Error("No valid questions found in the import text. Please check the format:\n\nQ: Your question here?\nA: Option A\nB: Option B\nC: Option C\nD: Option D\nAnswer: A\nExplanation: Your explanation here\n---");
      }
      // Insert questions into database
      const { error } = await supabase.from("questions").insert(
        questions.map(q => ({
          topic_id: q.topic_id,
          question_text: q.question_text,
          options: q.options,
          correct_answer: q.correct_answer,
          explanation: q.explanation || "",
        }))
      );
      if (error) throw error;

      setSuccessMessage(`Successfully imported ${questions.length} questions!`);
      toast({
        title: "Questions imported successfully",
        description: `Imported ${questions.length} questions to ${topics.find(t => t.id === topicId)?.title || 'selected topic'}`
      });

      // Clear form after successful import
      setImportText("");
      setTopicId("");

      onSuccess();
    } catch (error: unknown) {
      console.error("Error importing questions:", error);
      let message = "An error occurred";
      if (
        typeof error === "object" &&
        error !== null &&
        "message" in error &&
        typeof (error as { message?: unknown }).message === "string"
      ) {
        message = (error as { message: string }).message;
      }
      setError(message);
      toast({ 
        title: "Error importing questions", 
        description: message,
        variant: "destructive" 
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Card className="cyber-card p-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-5 w-5 mr-2 text-cyber-primary" />
            <h3 className="text-lg font-medium">Bulk Import Questions</h3>
          </div>
        </div>
        
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Errors</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Import Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {successMessage && (
          <Alert className="border-green-200 bg-green-50">
            <AlertCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Success</AlertTitle>
            <AlertDescription className="text-green-700">{successMessage}</AlertDescription>
          </Alert>
        )}
        
  <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="topic">Select Topic</Label>
            <Select value={topicId} onValueChange={setTopicId}>
              <SelectTrigger>
                <SelectValue placeholder="Select a topic" />
              </SelectTrigger>
              <SelectContent>
                {topics.map((topic) => (
                  <SelectItem key={topic.id} value={topic.id}>
                    {topic.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="importText">
              Paste Questions (Format: Q: Question, A: Option, B: Option, etc., Answer: X, Explanation: Text, ---)
            </Label>
            <Textarea
              id="importText"
              value={importText}
              onChange={(e) => setImportText(e.target.value)}
              placeholder={`Q: TechCorp has 500 employees across three offices in different cities. They need a centralized system to manage user accounts, passwords, and access to resources. Which solution would best meet their needs?\nA: Local user accounts on each computer\nB: Active Directory Domain Services\nC: Excel spreadsheet with user information\nD: Individual password files on each server\nAnswer: B\nExplanation: AD provides centralized management for large organizations.\n---\n\nQ: After installing the ADDS feature, what must be done to make the server functional as an Active Directory server?\nA: Restart the computer three times\nB: Promote the server to a Domain Controller\nC: Install additional RAM\nD: Configure firewall rules\nAnswer: B\nExplanation: Promotion configures the server as a functional DC.\n---`}
              rows={15}
              className="font-mono text-sm"
            />
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground mb-4">
            <Upload className="h-4 w-4 mr-2" />
            <span>Separate multiple questions with "---"</span>
          </div>
          
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-cyber-primary hover:bg-cyber-primary/90"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Importing..." : "Import Questions"}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default AdminQuestionImport;
