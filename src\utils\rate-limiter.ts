/**
 * Rate Limiter for Supabase Authentication Requests
 * Prevents 429 Too Many Requests errors by throttling token refresh and auth operations
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
  lastRequest: number;
}

interface RateLimitConfig {
  maxRequests: number;
  windowMs: number;
  cooldownMs: number;
}

class AuthRateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private configs: Map<string, RateLimitConfig> = new Map();

  constructor() {
    // Configure rate limits for different auth operations
    this.configs.set('token_refresh', {
      maxRequests: 5,        // Max 5 refresh attempts
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 5000       // 5 second cooldown between attempts
    });

    this.configs.set('auth_request', {
      maxRequests: 10,       // Max 10 auth requests
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 1000       // 1 second cooldown between attempts
    });

    this.configs.set('session_check', {
      maxRequests: 20,       // Max 20 session checks
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 500        // 500ms cooldown between attempts
    });

    // Clean up expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  /**
   * Check if a request is allowed for the given operation
   */
  public isAllowed(operation: string, identifier: string = 'global'): boolean {
    const config = this.configs.get(operation);
    if (!config) {
      console.warn(`No rate limit config found for operation: ${operation}`);
      return true;
    }

    const key = `${operation}:${identifier}`;
    const now = Date.now();
    const entry = this.limits.get(key);

    // If no entry exists, create one and allow the request
    if (!entry) {
      this.limits.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
        lastRequest: now
      });
      return true;
    }

    // Check if the window has expired
    if (now >= entry.resetTime) {
      this.limits.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
        lastRequest: now
      });
      return true;
    }

    // Check cooldown period
    if (now - entry.lastRequest < config.cooldownMs) {
      console.warn(`Rate limit cooldown active for ${operation}. Please wait ${config.cooldownMs - (now - entry.lastRequest)}ms`);
      return false;
    }

    // Check if we've exceeded the limit
    if (entry.count >= config.maxRequests) {
      const timeUntilReset = entry.resetTime - now;
      console.warn(`Rate limit exceeded for ${operation}. Reset in ${timeUntilReset}ms`);
      return false;
    }

    // Update the entry and allow the request
    entry.count++;
    entry.lastRequest = now;
    return true;
  }

  /**
   * Get the time until the rate limit resets for an operation
   */
  public getTimeUntilReset(operation: string, identifier: string = 'global'): number {
    const key = `${operation}:${identifier}`;
    const entry = this.limits.get(key);
    
    if (!entry) {
      return 0;
    }

    const now = Date.now();
    return Math.max(0, entry.resetTime - now);
  }

  /**
   * Get the remaining requests for an operation
   */
  public getRemainingRequests(operation: string, identifier: string = 'global'): number {
    const config = this.configs.get(operation);
    if (!config) {
      return Infinity;
    }

    const key = `${operation}:${identifier}`;
    const entry = this.limits.get(key);
    
    if (!entry) {
      return config.maxRequests;
    }

    const now = Date.now();
    if (now >= entry.resetTime) {
      return config.maxRequests;
    }

    return Math.max(0, config.maxRequests - entry.count);
  }

  /**
   * Reset rate limits for a specific operation and identifier
   */
  public reset(operation: string, identifier: string = 'global'): void {
    const key = `${operation}:${identifier}`;
    this.limits.delete(key);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.limits.entries()) {
      if (now >= entry.resetTime) {
        this.limits.delete(key);
      }
    }
  }

  /**
   * Get status information for debugging
   */
  public getStatus(): Record<string, any> {
    const status: Record<string, any> = {};
    
    for (const [operation, config] of this.configs.entries()) {
      status[operation] = {
        config,
        remaining: this.getRemainingRequests(operation),
        timeUntilReset: this.getTimeUntilReset(operation)
      };
    }

    return status;
  }
}

// Create a singleton instance
export const authRateLimiter = new AuthRateLimiter();

/**
 * Utility function to wait for rate limit reset
 */
export async function waitForRateLimit(operation: string, identifier?: string): Promise<void> {
  const timeUntilReset = authRateLimiter.getTimeUntilReset(operation, identifier);
  
  if (timeUntilReset > 0) {
    console.log(`Waiting ${timeUntilReset}ms for rate limit reset...`);
    await new Promise(resolve => setTimeout(resolve, timeUntilReset));
  }
}

/**
 * Wrapper function to execute auth operations with rate limiting
 */
export async function executeWithRateLimit<T>(
  operation: string,
  fn: () => Promise<T>,
  identifier?: string
): Promise<T> {
  if (!authRateLimiter.isAllowed(operation, identifier)) {
    const timeUntilReset = authRateLimiter.getTimeUntilReset(operation, identifier);
    throw new Error(`Rate limit exceeded for ${operation}. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`);
  }

  try {
    return await fn();
  } catch (error: any) {
    // If we get a 429 error, reset the rate limiter for this operation
    if (error.status === 429 || error.message?.includes('Too Many Requests')) {
      console.warn(`429 error detected for ${operation}, resetting rate limiter`);
      authRateLimiter.reset(operation, identifier);
    }
    throw error;
  }
}

/**
 * Check if an error is a rate limit error
 */
export function isRateLimitError(error: any): boolean {
  return error.status === 429 || 
         error.message?.includes('Too Many Requests') ||
         error.message?.includes('rate limit');
}
