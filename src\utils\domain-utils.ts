import { supabase } from "@/integrations/supabase/client";
import { Domain, DomainForUI, DomainWithDetails, DomainFilters, DomainEnrollment } from "@/types/domain";

/**
 * Fetches all active domains from the database
 */
export async function fetchAllDomains(): Promise<Domain[]> {
  try {
    console.log('🔍 Fetching domains from Supabase...');

    const { data, error } = await supabase
      .from("domains")
      .select("*")
      .eq("is_active", true)
      .order("sort_order");

    if (error) {
      console.error('❌ Supabase error fetching domains:', error);
      throw error;
    }

    console.log('✅ Successfully fetched domains:', data?.length || 0);
    console.log('📋 Domains data:', data);

    return data || [];
  } catch (error) {
    console.error('💥 Error fetching domains:', error);
    return [];
  }
}

/**
 * Enriches domains with topic counts
 */
export async function enrichWithTopicCounts(domains: DomainForUI[]): Promise<DomainForUI[]> {
  try {
    if (domains.length === 0) return domains;

    console.log('🔢 Fetching topic counts for domains...');
    
    const domainIds = domains.map(d => d.id);
    
    // Get topics from both direct relationship and junction table
    const [directTopicsResult, junctionTopicsResult] = await Promise.all([
      // Direct relationship topics (legacy)
      supabase
        .from("topics")
        .select("id, domain_id")
        .in("domain_id", domainIds)
        .eq("is_active", true),

      // Junction table topics (new many-to-many relationship)
      supabase
        .from("topic_domains")
        .select("domain_id, topic_id")
        .in("domain_id", domainIds)
    ]);

    if (directTopicsResult.error) {
      console.error('❌ Error fetching direct topics:', directTopicsResult.error);
    }

    if (junctionTopicsResult.error) {
      console.error('❌ Error fetching junction topics:', junctionTopicsResult.error);
    }

    // Count topics per domain from both sources
    const topicCountMap: { [key: string]: Set<string> } = {};

    // Initialize sets for each domain
    domainIds.forEach(domainId => {
      topicCountMap[domainId] = new Set();
    });

    // Add direct relationship topics
    (directTopicsResult.data || []).forEach((topic: any) => {
      if (topic.domain_id && topicCountMap[topic.domain_id]) {
        topicCountMap[topic.domain_id].add(topic.id);
      }
    });

    // Add junction table topics
    (junctionTopicsResult.data || []).forEach((mapping: any) => {
      if (mapping.domain_id && topicCountMap[mapping.domain_id]) {
        topicCountMap[mapping.domain_id].add(mapping.topic_id);
      }
    });

    // Convert sets to counts
    const finalTopicCounts = Object.keys(topicCountMap).reduce((acc: any, domainId: string) => {
      acc[domainId] = topicCountMap[domainId].size;
      return acc;
    }, {});

    console.log('📊 Topic counts:', finalTopicCounts);

    // Enrich domains with topic counts
    return domains.map(domain => ({
      ...domain,
      topicCount: finalTopicCounts[domain.id] || 0
    }));
  } catch (error) {
    console.error('💥 Error enriching with topic counts:', error);
    return domains;
  }
}

/**
 * Fetches a single domain by slug with detailed information
 */
export async function fetchDomainBySlug(slug: string): Promise<DomainWithDetails | null> {
  try {
    // First, get the domain basic information
    const { data: domainData, error: domainError } = await supabase
      .from("domains")
      .select(`
        *,
        domain_learning_paths(*),
        domain_subscription_plans(*)
      `)
      .eq("slug", slug)
      .eq("is_active", true)
      .single();

    if (domainError) throw domainError;
    if (!domainData) return null;

    // Get topics from both direct relationship and junction table
    const [directTopicsResult, junctionTopicsResult] = await Promise.all([
      // Direct relationship topics (legacy)
      supabase
        .from("topics")
        .select("id, title, description, difficulty, is_premium")
        .eq("domain_id", domainData.id)
        .eq("is_active", true),

      // Junction table topics (new many-to-many relationship)
      supabase
        .from("topic_domains")
        .select(`
          topics!inner(id, title, description, difficulty, is_premium)
        `)
        .eq("domain_id", domainData.id)
        .eq("topics.is_active", true)
    ]);

    // Combine topics from both sources, avoiding duplicates
    const directTopics = directTopicsResult.data || [];
    const junctionTopics = (junctionTopicsResult.data || []).map((item: any) => item.topics);

    // Create a map to avoid duplicates
    const topicsMap = new Map();

    // Add direct topics
    directTopics.forEach((topic: any) => {
      topicsMap.set(topic.id, topic);
    });

    // Add junction topics (will overwrite if duplicate, which is fine)
    junctionTopics.forEach((topic: any) => {
      topicsMap.set(topic.id, topic);
    });

    // Convert map back to array
    const allTopics = Array.from(topicsMap.values());

    // Get question counts for all topics
    const topicIds = allTopics.map((t: any) => t.id);
    const { data: questionCounts } = await supabase
      .from("questions")
      .select("topic_id")
      .in("topic_id", topicIds);

    const questionCountMap = questionCounts?.reduce((acc: any, q: any) => {
      acc[q.topic_id] = (acc[q.topic_id] || 0) + 1;
      return acc;
    }, {}) || {};

    return {
      id: domainData.id,
      name: domainData.name,
      slug: domainData.slug,
      description: domainData.description || "",
      icon: domainData.icon || "shield",
      colorTheme: domainData.color_theme || "#3B82F6",
      difficultyLevel: domainData.difficulty_level,
      estimatedDurationWeeks: domainData.estimated_duration_weeks,
      prerequisites: domainData.prerequisites || [],
      isActive: domainData.is_active,
      sortOrder: domainData.sort_order,
      learningPaths: domainData.domain_learning_paths || [],
      topics: allTopics.map((topic: any) => ({
        ...topic,
        question_count: questionCountMap[topic.id] || 0
      })),
      subscriptionPlan: domainData.domain_subscription_plans?.[0]
    };
  } catch (error) {
    console.error('Error fetching domain by slug:', error);
    return null;
  }
}

/**
 * Converts database domains to UI-friendly format
 */
export function convertDomainsToUIFormat(domains: Domain[]): DomainForUI[] {
  return domains.map(domain => ({
    id: domain.id,
    name: domain.name,
    slug: domain.slug,
    description: domain.description || "",
    icon: domain.icon || "shield",
    colorTheme: domain.color_theme || "#3B82F6",
    difficultyLevel: domain.difficulty_level,
    estimatedDurationWeeks: domain.estimated_duration_weeks,
    prerequisites: domain.prerequisites || [],
    isActive: domain.is_active,
    sortOrder: domain.sort_order,
    // default false; we'll enrich with real ownership below if needed
    userHasDomainPass: false,
    topicCount: 0, // Will be populated by enrichWithTopicCounts
  }));
}

/**
 * Enrich a list of domains with ownership flags for a user using active entitlements
 */
export async function markOwnedDomains(domains: DomainForUI[], userId?: string | null): Promise<DomainForUI[]> {
  if (!userId || domains.length === 0) return domains;
  try {
    const domainIds = domains.map(d => d.id);
    const { data } = await supabase
      .from('v_active_entitlements' as any)
      .select('ref_id')
      .eq('user_id', userId)
      .eq('entitlement_type', 'domain')
      .in('ref_id', domainIds);
    const ownedSet = new Set((data || []).map((r: any) => r.ref_id));
    return domains.map(d => ({ ...d, userHasDomainPass: ownedSet.has(d.id) }));
  } catch {
    return domains;
  }
}

/**
 * Filters domains based on provided criteria
 */
export function filterDomains(domains: DomainForUI[], filters: DomainFilters): DomainForUI[] {
  return domains.filter(domain => {
    // Difficulty filter
    if (filters.difficulty && domain.difficultyLevel !== filters.difficulty) {
      return false;
    }

    // Duration filter
    if (filters.duration) {
      const weeks = domain.estimatedDurationWeeks;
      switch (filters.duration) {
        case 'short':
          if (weeks > 4) return false;
          break;
        case 'medium':
          if (weeks < 5 || weeks > 8) return false;
          break;
        case 'long':
          if (weeks < 9) return false;
          break;
      }
    }

    // Prerequisites filter
    if (filters.hasPrerequisites !== undefined) {
      const hasPrereqs = domain.prerequisites.length > 0;
      if (filters.hasPrerequisites !== hasPrereqs) return false;
    }

    // Search query filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      const searchableText = `${domain.name} ${domain.description}`.toLowerCase();
      if (!searchableText.includes(query)) return false;
    }

    return true;
  });
}

/**
 * Gets domain enrollment status for a user
 */
export async function getDomainEnrollmentStatus(
  userId: string, 
  domainId: string
): Promise<DomainEnrollment> {
  try {
    // Check if user has an active subscription for this domain
    const { data: subscriptionData } = await supabase
      .from("subscriptions")
      .select("*")
      .eq("user_id", userId)
      .like("plan_id", `domain-%`)
      .eq("is_active", true)
      .gte("end_date", new Date().toISOString());

    // Check user progress in this domain
    const { data: progressData } = await supabase
      .from("user_domain_progress")
      .select("*")
      .eq("user_id", userId)
      .eq("domain_id", domainId);

    const hasActiveSubscription = subscriptionData?.some(sub => 
      sub.plan_id.includes(domainId.split('-').pop() || '')
    ) || false;

    return {
      domainId,
      isEnrolled: progressData && progressData.length > 0,
      hasActiveSubscription,
      subscriptionPlanId: subscriptionData?.[0]?.plan_id,
      enrollmentDate: progressData?.[0]?.started_at,
      progress: progressData?.[0]
    };
  } catch (error) {
    console.error('Error getting domain enrollment status:', error);
    return {
      domainId,
      isEnrolled: false,
      hasActiveSubscription: false
    };
  }
}

/**
 * Gets difficulty level color for UI display
 */
export function getDifficultyColor(difficulty: string): string {
  switch (difficulty) {
    case 'beginner':
      return 'text-green-600 bg-green-100';
    case 'intermediate':
      return 'text-blue-600 bg-blue-100';
    case 'advanced':
      return 'text-orange-600 bg-orange-100';
    case 'expert':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}

/**
 * Gets duration badge text and color
 */
export function getDurationBadge(weeks: number): { text: string; color: string } {
  if (weeks <= 4) {
    return { text: `${weeks} weeks`, color: 'text-green-600 bg-green-100' };
  } else if (weeks <= 8) {
    return { text: `${weeks} weeks`, color: 'text-blue-600 bg-blue-100' };
  } else {
    return { text: `${weeks} weeks`, color: 'text-purple-600 bg-purple-100' };
  }
}

/**
 * Checks if user meets domain prerequisites
 */
export async function checkDomainPrerequisites(
  userId: string, 
  domain: DomainForUI
): Promise<{ met: boolean; missing: string[] }> {
  if (domain.prerequisites.length === 0) {
    return { met: true, missing: [] };
  }

  try {
    // Get user's completed domains
    const { data: completedProgress } = await supabase
      .from("user_domain_progress")
      .select("domain_id, completion_percentage")
      .eq("user_id", userId)
      .gte("completion_percentage", 80); // Consider 80%+ as completed

    const completedDomainIds = completedProgress?.map(p => p.domain_id) || [];

    // Get domain slugs for completed domains
    const { data: completedDomains } = await supabase
      .from("domains")
      .select("slug")
      .in("id", completedDomainIds);

    const completedSlugs = completedDomains?.map(d => d.slug) || [];
    const missingPrereqs = domain.prerequisites.filter(prereq => 
      !completedSlugs.includes(prereq)
    );

    return {
      met: missingPrereqs.length === 0,
      missing: missingPrereqs
    };
  } catch (error) {
    console.error('Error checking domain prerequisites:', error);
    return { met: false, missing: domain.prerequisites };
  }
}
