import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Search, Plus, Trash2, Eye } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

interface Topic {
  id: string;
  title: string;
  description: string | null;
  difficulty: string | null;
  is_active: boolean;
  domain_id: string | null;
}

interface Domain {
  id: string;
  name: string;
  slug: string;
  description: string | null;
  is_active: boolean;
}

interface TopicDomainMapping {
  id: string;
  topic_id: string;
  domain_id: string;
  created_at: string;
  topic: Topic;
  domain: Domain;
}

const AdminTopicDomainAssignment = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [domains, setDomains] = useState<Domain[]>([]);
  const [mappings, setMappings] = useState<TopicDomainMapping[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDomain, setSelectedDomain] = useState<string>("all");
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [selectedTopics, setSelectedTopics] = useState<string[]>([]);
  const [targetDomain, setTargetDomain] = useState<string>("");

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('AdminTopicDomainAssignment: Loading data...');

      // Load topics
      const { data: topicsData, error: topicsError } = await supabase
        .from('topics')
        .select('*')
        .eq('is_active', true)
        .order('title');

      if (topicsError) {
        console.error('Error loading topics:', topicsError);
        throw topicsError;
      }
      console.log(`Loaded ${topicsData?.length || 0} topics`);

      // Load domains
      const { data: domainsData, error: domainsError } = await supabase
        .from('domains')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (domainsError) {
        console.error('Error loading domains:', domainsError);
        throw domainsError;
      }
      console.log(`Loaded ${domainsData?.length || 0} domains`);

      // Load existing mappings with related data
      const { data: mappingsData, error: mappingsError } = await supabase
        .from('topic_domains')
        .select(`
          *,
          topic:topics(*),
          domain:domains(*)
        `)
        .order('created_at', { ascending: false });

      if (mappingsError) {
        console.error('Error loading topic-domain mappings:', mappingsError);
        throw mappingsError;
      }
      console.log(`Loaded ${mappingsData?.length || 0} topic-domain mappings`);

      setTopics(topicsData || []);
      setDomains(domainsData || []);
      setMappings(mappingsData || []);

    } catch (error: any) {
      console.error('Error loading data:', error);

      // Provide more specific error messages
      let errorMessage = "Failed to load data. Please try again.";
      if (error?.message?.includes('topic_domains')) {
        errorMessage = "Topic-domain mapping table not found. Please contact administrator.";
      } else if (error?.message?.includes('permission')) {
        errorMessage = "Permission denied. Please check your admin access.";
      } else if (error?.message?.includes('network')) {
        errorMessage = "Network error. Please check your connection.";
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const assignTopicsToDomain = async () => {
    if (!targetDomain || selectedTopics.length === 0) {
      toast({
        title: "Error",
        description: "Please select a domain and at least one topic.",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Get current user ID for RLS policy compliance
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError) {
        throw new Error(`Authentication error: ${userError.message}`);
      }

      if (!user) {
        throw new Error("You must be logged in to assign topics to domains.");
      }

      // Create mappings for selected topics with created_by field
      const mappingsToInsert = selectedTopics.map(topicId => ({
        topic_id: topicId,
        domain_id: targetDomain,
        created_by: user.id, // Include created_by field for RLS policy compliance
      }));

      const { error } = await supabase
        .from('topic_domains')
        .insert(mappingsToInsert);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Successfully assigned ${selectedTopics.length} topic(s) to domain.`,
      });

      // Reset form and reload data
      setSelectedTopics([]);
      setTargetDomain("");
      setShowAssignDialog(false);
      await loadData();

    } catch (error: any) {
      console.error('Error assigning topics:', error);

      // Provide specific error messages for common issues
      let errorMessage = "Failed to assign topics. Please try again.";

      if (error.message?.includes('row-level security policy')) {
        errorMessage = "Permission denied. Please ensure you have admin privileges to assign topics.";
      } else if (error.message?.includes('Authentication error')) {
        errorMessage = error.message;
      } else if (error.message?.includes('logged in')) {
        errorMessage = error.message;
      } else if (error.message?.includes('duplicate key')) {
        errorMessage = "One or more topics are already assigned to this domain.";
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const removeMapping = async (mappingId: string) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('topic_domains')
        .delete()
        .eq('id', mappingId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Topic-domain mapping removed successfully.",
      });

      await loadData();

    } catch (error: any) {
      console.error('Error removing mapping:', error);
      toast({
        title: "Error",
        description: "Failed to remove mapping. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Filter topics and mappings based on search and domain selection
  const filteredTopics = topics.filter(topic => {
    const matchesSearch = topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (topic.description && topic.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });

  const filteredMappings = mappings.filter(mapping => {
    const matchesSearch = mapping.topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mapping.domain.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDomain = selectedDomain === "all" || mapping.domain_id === selectedDomain;
    return matchesSearch && matchesDomain;
  });

  // Get topics that are not assigned to the target domain
  const availableTopics = topics.filter(topic => {
    if (!targetDomain) return true;
    return !mappings.some(mapping => 
      mapping.topic_id === topic.id && mapping.domain_id === targetDomain
    );
  });

  const getDifficultyColor = (difficulty: string | null) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && topics.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading topic-domain assignments...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Topic-Domain Assignment</h2>
          <p className="text-muted-foreground">
            Manage which topics belong to which domains
          </p>
        </div>
        
        <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Assign Topics
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Assign Topics to Domain</DialogTitle>
              <DialogDescription>
                Select topics and assign them to a domain
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Domain Selection */}
              <div>
                <Label htmlFor="target-domain">Target Domain</Label>
                <Select value={targetDomain} onValueChange={setTargetDomain}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a domain" />
                  </SelectTrigger>
                  <SelectContent>
                    {domains.map((domain) => (
                      <SelectItem key={domain.id} value={domain.id}>
                        {domain.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Topic Selection */}
              <div>
                <Label>Available Topics</Label>
                <div className="max-h-60 overflow-y-auto border rounded-md p-2 space-y-2">
                  {availableTopics.map((topic) => (
                    <div key={topic.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`topic-${topic.id}`}
                        checked={selectedTopics.includes(topic.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedTopics([...selectedTopics, topic.id]);
                          } else {
                            setSelectedTopics(selectedTopics.filter(id => id !== topic.id));
                          }
                        }}
                      />
                      <Label htmlFor={`topic-${topic.id}`} className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <span>{topic.title}</span>
                          <Badge className={getDifficultyColor(topic.difficulty)}>
                            {topic.difficulty || 'Unknown'}
                          </Badge>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowAssignDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={assignTopicsToDomain} disabled={loading}>
                  {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Assign Topics
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Topics/Domains</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by topic or domain name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <Label htmlFor="domain-filter">Filter by Domain</Label>
              <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Domains</SelectItem>
                  {domains.map((domain) => (
                    <SelectItem key={domain.id} value={domain.id}>
                      {domain.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Mappings */}
      <Card>
        <CardHeader>
          <CardTitle>Current Topic-Domain Assignments</CardTitle>
          <CardDescription>
            {filteredMappings.length} assignment(s) found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredMappings.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No topic-domain assignments found.
            </div>
          ) : (
            <div className="space-y-2">
              {filteredMappings.map((mapping) => (
                <div
                  key={mapping.id}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{mapping.topic.title}</span>
                      <Badge className={getDifficultyColor(mapping.topic.difficulty)}>
                        {mapping.topic.difficulty || 'Unknown'}
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Domain: <span className="font-medium">{mapping.domain.name}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeMapping(mapping.id)}
                      disabled={loading}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminTopicDomainAssignment;
