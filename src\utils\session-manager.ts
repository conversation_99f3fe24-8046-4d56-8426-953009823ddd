/**
 * Session Management Utility
 * Handles authentication session refresh and validation
 */

import { supabase } from '@/integrations/supabase/client';
import { authRateLimiter, executeWithRateLimit, isRateLimitError } from './rate-limiter';

interface SessionStatus {
  isValid: boolean;
  session: any;
  error?: string;
  refreshed?: boolean;
}

/**
 * Check if the current session is valid and refresh if needed
 */
export async function validateAndRefreshSession(): Promise<SessionStatus> {
  try {
    // Check rate limit for session operations
    if (!authRateLimiter.isAllowed('session_check')) {
      return {
        isValid: false,
        session: null,
        error: 'Rate limit exceeded for session checks'
      };
    }

    // Get current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.warn('Session error:', sessionError);
      return {
        isValid: false,
        session: null,
        error: sessionError.message
      };
    }

    // If no session, return invalid
    if (!session) {
      return {
        isValid: false,
        session: null,
        error: 'No active session'
      };
    }

    // Check if session is expired or about to expire (within 5 minutes)
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = session.expires_at || 0;
    const isExpired = expiresAt <= now;
    const isAboutToExpire = expiresAt <= (now + 300); // 5 minutes

    if (isExpired || isAboutToExpire) {
      console.log('Session expired or about to expire, attempting refresh...');

      // Check rate limit for token refresh
      if (!authRateLimiter.isAllowed('token_refresh')) {
        const timeUntilReset = authRateLimiter.getTimeUntilReset('token_refresh');
        console.warn(`Token refresh rate limited. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`);
        return {
          isValid: false,
          session: null,
          error: `Token refresh rate limited. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`
        };
      }

      try {
        // Try to refresh the session with rate limiting
        const { data: refreshData, error: refreshError } = await executeWithRateLimit(
          'token_refresh',
          () => supabase.auth.refreshSession()
        );

        if (refreshError) {
          console.error('Failed to refresh session:', refreshError);

          // If it's a rate limit error, provide specific guidance
          if (isRateLimitError(refreshError)) {
            return {
              isValid: false,
              session: null,
              error: 'Too many refresh attempts. Please wait before trying again.'
            };
          }

          return {
            isValid: false,
            session: null,
            error: 'Failed to refresh session: ' + refreshError.message
          };
        }

        if (refreshData.session) {
          console.log('Session refreshed successfully');
          return {
            isValid: true,
            session: refreshData.session,
            refreshed: true
          };
        }
      } catch (rateLimitError: any) {
        console.warn('Token refresh rate limited:', rateLimitError.message);
        return {
          isValid: false,
          session: null,
          error: rateLimitError.message
        };
      }
    }

    // Session is valid
    return {
      isValid: true,
      session,
      refreshed: false
    };

  } catch (error) {
    console.error('Error validating session:', error);
    return {
      isValid: false,
      session: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Execute a Supabase query with automatic session validation and refresh
 * For public data, allows fallback to anonymous access
 */
export async function executeWithValidSession<T>(
  queryFn: () => Promise<{ data: T | null; error: any }>,
  allowAnonymous: boolean = false
): Promise<{ data: T | null; error: any; sessionRefreshed?: boolean; isAnonymous?: boolean }> {

  // First, validate and refresh session if needed
  const sessionStatus = await validateAndRefreshSession();

  // If session is invalid and we don't allow anonymous access, return error
  if (!sessionStatus.isValid && !allowAnonymous) {
    return {
      data: null,
      error: {
        code: 'SESSION_INVALID',
        message: sessionStatus.error || 'Invalid session'
      }
    };
  }

  // Execute the query
  try {
    const result = await queryFn();

    // If we get a JWT expired error and anonymous access is allowed, try without auth
    if (result.error && (result.error.code === 'PGRST301' || result.error.message?.includes('JWT expired'))) {
      console.log('JWT expired during query...');

      if (allowAnonymous) {
        console.log('Attempting anonymous access for public data...');

        // DON'T sign out - just try the query without authentication
        // This prevents automatic sign-outs that cause admin session loss

        // Retry the query without authentication
        const anonymousResult = await queryFn();
        return {
          ...anonymousResult,
          isAnonymous: true
        };
      } else {
        // Try to refresh session one more time
        const refreshStatus = await validateAndRefreshSession();
        if (refreshStatus.isValid) {
          console.log('Session refreshed, retrying query...');
          const retryResult = await queryFn();
          return {
            ...retryResult,
            sessionRefreshed: true
          };
        }
      }
    }

    return {
      ...result,
      sessionRefreshed: sessionStatus.refreshed,
      isAnonymous: !sessionStatus.isValid
    };

  } catch (error) {
    return {
      data: null,
      error: {
        code: 'QUERY_ERROR',
        message: error instanceof Error ? error.message : 'Unknown query error'
      }
    };
  }
}

/**
 * Check if an error is related to authentication/session issues
 */
export function isAuthError(error: any): boolean {
  if (!error) return false;
  
  const authErrorCodes = ['PGRST301', 'PGRST401', 'SESSION_INVALID'];
  const authErrorMessages = ['JWT expired', 'permission denied', 'unauthorized', 'invalid session'];
  
  return authErrorCodes.includes(error.code) || 
         authErrorMessages.some(msg => error.message?.toLowerCase().includes(msg));
}

/**
 * Get user-friendly error message for authentication errors
 */
export function getAuthErrorMessage(error: any): string {
  if (!error) return 'Unknown error';
  
  if (error.code === 'PGRST301' || error.message?.includes('JWT expired')) {
    return 'Your session has expired. Please refresh the page to continue.';
  }
  
  if (error.code === 'PGRST401' || error.message?.includes('permission denied')) {
    return 'You don\'t have permission to access this data. Please check your account status.';
  }
  
  if (error.code === 'SESSION_INVALID') {
    return 'Please sign in to continue.';
  }
  
  return error.message || 'Authentication error occurred.';
}
