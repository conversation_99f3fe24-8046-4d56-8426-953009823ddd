import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth';

export interface QuizSessionState {
  sessionId: string;
  topicId: string;
  currentQuestionIndex: number;
  selectedAnswers: Record<number, number>;
  timeRemaining?: number;
  startTime: number;
  questions: any[];
  isCompleted: boolean;
  score?: number;
  totalQuestions: number;
}

const STORAGE_KEY = 'secquiz_active_session';
const SESSION_EXPIRY_HOURS = 2;

export const useQuizSessionPersistence = () => {
  const { user } = useAuth();
  const [activeSession, setActiveSession] = useState<QuizSessionState | null>(null);

  // Load session from localStorage on mount
  useEffect(() => {
    loadActiveSession();
  }, [user]);

  const loadActiveSession = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return;

      const sessionData: QuizSessionState = JSON.parse(stored);
      
      // Check if session is expired
      const now = Date.now();
      const sessionAge = now - sessionData.startTime;
      const maxAge = SESSION_EXPIRY_HOURS * 60 * 60 * 1000; // 2 hours in milliseconds
      
      if (sessionAge > maxAge || sessionData.isCompleted) {
        // Session expired or completed, remove it
        clearActiveSession();
        return;
      }

      // Validate session belongs to current user (if logged in)
      if (user && sessionData.sessionId && !sessionData.sessionId.includes('guest')) {
        // For authenticated users, we could validate against database
        // For now, just load the session
        setActiveSession(sessionData);
      } else if (!user && sessionData.sessionId.includes('guest')) {
        // Guest session
        setActiveSession(sessionData);
      } else {
        // Session doesn't match current user state
        clearActiveSession();
      }
    } catch (error) {
      console.error('Error loading quiz session:', error);
      clearActiveSession();
    }
  }, [user]);

  const saveActiveSession = useCallback((sessionState: QuizSessionState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(sessionState));
      setActiveSession(sessionState);
    } catch (error) {
      console.error('Error saving quiz session:', error);
    }
  }, []);

  const updateSessionState = useCallback((updates: Partial<QuizSessionState>) => {
    if (!activeSession) return;

    const updatedSession = { ...activeSession, ...updates };
    saveActiveSession(updatedSession);
  }, [activeSession, saveActiveSession]);

  const clearActiveSession = useCallback(() => {
    try {
      localStorage.removeItem(STORAGE_KEY);
      setActiveSession(null);
    } catch (error) {
      console.error('Error clearing quiz session:', error);
    }
  }, []);

  const hasActiveSession = useCallback(() => {
    return activeSession !== null && !activeSession.isCompleted;
  }, [activeSession]);

  const getSessionProgress = useCallback(() => {
    if (!activeSession) return null;

    const answeredQuestions = Object.keys(activeSession.selectedAnswers).length;
    const progressPercentage = (answeredQuestions / activeSession.totalQuestions) * 100;
    
    return {
      currentQuestion: activeSession.currentQuestionIndex + 1,
      totalQuestions: activeSession.totalQuestions,
      answeredQuestions,
      progressPercentage: Math.round(progressPercentage),
      timeElapsed: Date.now() - activeSession.startTime,
    };
  }, [activeSession]);

  const canResumeSession = useCallback((topicId: string) => {
    if (!activeSession) return false;
    return activeSession.topicId === topicId && !activeSession.isCompleted;
  }, [activeSession]);

  const startNewSession = useCallback((sessionData: Omit<QuizSessionState, 'startTime'>) => {
    const newSession: QuizSessionState = {
      ...sessionData,
      startTime: Date.now(),
    };
    saveActiveSession(newSession);
  }, [saveActiveSession]);

  const completeSession = useCallback((score: number) => {
    if (!activeSession) return;

    const completedSession = {
      ...activeSession,
      isCompleted: true,
      score,
    };
    
    saveActiveSession(completedSession);
    
    // Clear the session after a short delay to allow for score display
    setTimeout(() => {
      clearActiveSession();
    }, 5000);
  }, [activeSession, saveActiveSession, clearActiveSession]);

  const abandonSession = useCallback(() => {
    clearActiveSession();
  }, [clearActiveSession]);

  // Auto-save session state periodically
  useEffect(() => {
    if (!activeSession || activeSession.isCompleted) return;

    const interval = setInterval(() => {
      // Update the session with current timestamp to keep it fresh
      updateSessionState({ startTime: activeSession.startTime });
    }, 30000); // Save every 30 seconds

    return () => clearInterval(interval);
  }, [activeSession, updateSessionState]);

  // Handle page visibility change to pause/resume timers
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!activeSession || activeSession.isCompleted) return;

      if (document.hidden) {
        // Page is hidden, save current state
        updateSessionState({});
      } else {
        // Page is visible again, could resume timers here
        loadActiveSession();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [activeSession, updateSessionState, loadActiveSession]);

  // Handle beforeunload to save session
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (activeSession && !activeSession.isCompleted) {
        // Save current state before page unload
        updateSessionState({});
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [activeSession, updateSessionState]);

  return {
    activeSession,
    hasActiveSession,
    canResumeSession,
    getSessionProgress,
    startNewSession,
    updateSessionState,
    completeSession,
    abandonSession,
    clearActiveSession,
    loadActiveSession,
  };
};
