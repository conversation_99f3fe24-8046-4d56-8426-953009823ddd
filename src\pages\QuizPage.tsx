import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import ResumeQuizDialog from "@/components/ResumeQuizDialog";
import { Timer, Trophy, BookOpen, AlertCircle, Lightbulb as LightbulbIcon, Loader2, ChevronLeft, ChevronRight, GraduationCap } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useQuizSessionPersistence } from "@/hooks/use-quiz-session-persistence";
import { Link, useParams, useNavigate, useSearchParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { canAccessTopicSync, canAccessTopic, isTopicPremium, isUserPremium, PUBLIC_TOPICS, AUTHENTICATED_TOPICS } from "@/utils/topic-access";
import { featureFlags } from "@/config";
import { fetchLearningMaterialsByTopic, LearningMaterial } from "@/utils/fetch-learning-materials";
import { Input } from "@/components/ui/input";
import type { User } from "@supabase/supabase-js";
import { parseCorrectAnswer, parseQuestionOptions, logAnswerValidation, safeValidateAnswer, validateAnswer } from "@/utils/answer-validation";
import { QuizRandomizationService, type RandomizedQuestion, type QuizSessionResult } from "@/services/quiz-randomization-service";
import { UserPreferencesService } from "@/services/user-preferences-service";
import { QuizAnalyticsService } from "@/services/quiz-analytics-service";
import { generateGuestUserUUID } from "@/utils/uuid-helpers";

// Utility function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};

// Define types
interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: number;
  originalCorrectAnswer: string | number; // Store original value for validation
  explanation: string;
  is_premium?: boolean;
}

// Extended interface for randomized questions
interface QuizQuestion extends RandomizedQuestion {
  text: string;
  options: string[];
  explanation: string;
  is_premium?: boolean;
}

interface Quiz {
  id: string;
  title: string;
  description: string;
  is_premium: boolean;
  questions: QuizQuestion[];
}

// Helper function to check if user is premium - using the utility function for consistency
const isPremiumUser = (user: User | null) => {
  return isUserPremium(user);
};

const QuizPage = () => {
  const { topicId } = useParams<{ topicId: string }>();
  const [searchParams] = useSearchParams();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // State variables
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60); // 60 seconds per question
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [freeQuestionsUsed, setFreeQuestionsUsed] = useState(0);
  const [showFreeLimit, setShowFreeLimit] = useState(false);
  const [reviewMode, setReviewMode] = useState(false);
  const [userAnswers, setUserAnswers] = useState<(number | null)[]>([]);
  const startTimeRef = useRef<Date>(new Date());
  const [learningMaterials, setLearningMaterials] = useState<LearningMaterial[]>([]);
  const [showLearningMaterials, setShowLearningMaterials] = useState(false);

  // Session persistence
  const sessionPersistence = useQuizSessionPersistence();
  const [showResumeDialog, setShowResumeDialog] = useState(false);

  // Quiz session state
  const [quizSession, setQuizSession] = useState<QuizSessionResult | null>(null);
  const [sessionError, setSessionError] = useState<string | null>(null);

  // Coupon UI state
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponError, setCouponError] = useState("");

  // Function to resume existing session
  const resumeSession = () => {
    const activeSession = sessionPersistence.activeSession;
    if (!activeSession || !quiz) return;

    // Restore session state
    setCurrentQuestionIndex(activeSession.currentQuestionIndex);
    setScore(activeSession.score || 0);
    setTimeLeft(activeSession.timeRemaining || 60);

    // Restore user answers
    const answers = new Array(quiz.questions.length).fill(null);
    Object.entries(activeSession.selectedAnswers).forEach(([index, answer]) => {
      answers[parseInt(index)] = answer;
    });
    setUserAnswers(answers);

    // Update start time reference
    startTimeRef.current = new Date(activeSession.startTime);

    setShowResumeDialog(false);
  };

  // Function to start new session
  const startNewSession = () => {
    if (!quiz || !quizSession || !topicId) return;

    const sessionState = {
      sessionId: quizSession.sessionId,
      topicId,
      currentQuestionIndex: 0,
      selectedAnswers: {},
      timeRemaining: 60,
      questions: quiz.questions,
      isCompleted: false,
      totalQuestions: quiz.questions.length,
    };

    sessionPersistence.startNewSession(sessionState);
    setShowResumeDialog(false);
  };

  // Function to save quiz attempt to database
  const saveQuizAttempt = async (finalScore: number, totalQuestions: number, timeTaken: number) => {
    if (!user || !quiz || !quizSession) return;

    try {
      // Complete the quiz session
      await QuizRandomizationService.completeQuizSession(
        quizSession.sessionId,
        user.id,
        finalScore,
        timeTaken
      );

      // Also save to quiz_attempts for backward compatibility
      const { error } = await supabase
        .from('quiz_attempts')
        .insert({
          user_id: user.id,
          topic_id: quiz?.id,
          score: finalScore,
          total_questions: totalQuestions,
          answers: userAnswers,
          time_taken: timeTaken
        });

      if (error) {
        console.error('Error saving quiz attempt:', error);
      } else {
        console.log('Quiz attempt saved successfully');
      }
    } catch (error) {
      console.error('Error in saveQuizAttempt:', error);
    }
  };

  // Record question analytics
  const recordQuestionAnalytics = async (
    questionId: string,
    answeredCorrectly: boolean,
    selectedOption: number,
    timeToAnswer?: number
  ) => {
    if (!user || !quizSession) return;

    try {
      await QuizRandomizationService.recordQuestionAnalytics(
        questionId,
        user.id,
        quizSession.sessionId,
        answeredCorrectly,
        selectedOption,
        timeToAnswer
      );
    } catch (error) {
      console.error('Error recording question analytics:', error);
    }
  };

  // Load quiz data based on topic ID using randomization service
  useEffect(() => {
    const loadQuiz = async () => {
      try {
        setLoading(true);
        setSessionError(null);

        // Reset learning materials state
        setLearningMaterials([]);

        // Make sure we have a valid topicId
        if (!topicId) {
          console.error("No topic ID provided");
          navigate("/quizzes");
          return;
        }

        // Convert topicId to string to ensure correct lookup
        const topicIdStr = String(topicId);

        // First, fetch the topic details from Supabase
        const { data: topicData, error: topicError } = await supabase
          .from("topics")
          .select("*")
          .eq("id", topicIdStr)
          .single();

        if (topicError) {
          console.error(`Topic not found: ${topicError.message}`);
          toast({
            title: "Topic Not Found",
            description: "The requested quiz topic could not be found.",
            variant: "destructive",
          });
          navigate("/quizzes");
          return;
        }

        // Debug user information
        console.log('User:', user ? { email: user.email, id: user.id } : 'No user');

        // Check if this is a premium topic based on our custom logic
        const isPremiumTopic = isTopicPremium(topicData.title, topicData.difficulty);

        // First check if this is a free topic (either public or for authenticated users)
        const isFreeForRegistered = PUBLIC_TOPICS.includes(topicData.title) || AUTHENTICATED_TOPICS.includes(topicData.title);

        // If the user is logged in and the topic is free for registered users, they can access it
        if (user && isFreeForRegistered) {
          console.log(`Topic: ${topicData.title} is free for registered users, granting access`);
          // Continue with quiz loading
        }
        // If the topic is public (accessible to everyone), allow access
        else if (PUBLIC_TOPICS.includes(topicData.title)) {
          console.log(`Topic: ${topicData.title} is public, granting access to everyone`);
          // Continue with quiz loading
        }
        // For premium topics, check if user has access
        else {
          // First do a quick synchronous check for immediate UI feedback
          let canAccess = canAccessTopicSync(topicData.title, topicData.id, user);
          console.log(`Topic: ${topicData.title}, Premium: ${isPremiumTopic}, Can Access (sync): ${canAccess}`);

          // If the sync check says user can't access, do a more thorough async check
          if (!canAccess) {
            try {
              // Do the async check that queries the database
              const asyncCanAccess = await canAccessTopic(topicData.title, topicData.id, user);
              console.log(`Topic: ${topicData.title}, Can Access (async): ${asyncCanAccess}`);

              // Update the access status if the async check says user can access
              if (asyncCanAccess) {
                canAccess = true;
              }
            } catch (error) {
              console.error("Error checking async access:", error);
              // Keep the result from the sync check
            }
          }

          // If user cannot access this topic, redirect to quizzes page
          if (!canAccess) {
            // In the section where user is denied access to Cybersecurity Foundation - Easy
            if (topicData.title === "Cybersecurity Foundation - Easy" && user) {
              setShowCouponInput(true);
              // Do NOT redirect away; just return to allow coupon input UI to render
              return;
            }
            // Different messages based on authentication status
            if (!user) {
              toast({
                title: "Authentication Required",
                description: "Please sign in to access this quiz",
                variant: "destructive",
              });
            } else if (isPremiumTopic) {
              toast({
                title: "Premium Content",
                description: "This quiz requires a premium subscription",
                variant: "destructive",
              });
            } else {
              toast({
                title: "Access Denied",
                description: "You don't have permission to access this quiz",
                variant: "destructive",
              });
            }
            navigate("/quizzes");
            return;
          }
        }

        // Generate randomized quiz session using the randomization service
        console.log('Generating randomized quiz session for topic ID:', topicIdStr);

        try {
          // First check if there are any questions available for this topic
          console.log('Checking questions for topic:', topicIdStr);
          const { data: questionCheck, error: questionCheckError } = await supabase
            .from('questions')
            .select('id', { count: 'exact' })
            .eq('topic_id', topicIdStr);

          if (questionCheckError) {
            console.error('Error checking questions:', questionCheckError);
            throw new Error(`Unable to check question availability: ${questionCheckError.message}`);
          }

          console.log('Question check result:', questionCheck);
          
          if (!questionCheck || questionCheck.length === 0) {
            console.error('No questions found for topic:', topicIdStr);
            throw new Error('No questions available for this topic');
          }

          console.log(`Found ${questionCheck.length} questions for topic ${topicIdStr}`);
          // For guests, we need to handle the case where user is not logged in
          if (!user) {
            // For public topics, allow guest access with a temporary session
            if (PUBLIC_TOPICS.includes(topicData.title)) {
              // Create a temporary user ID for guest sessions (proper UUID)
              const guestUserId = generateGuestUserUUID();

              const sessionResult = await QuizRandomizationService.generateQuizSession(
                topicIdStr,
                guestUserId,
                10 // Default quiz length for guests
              );

              console.log('Generated guest quiz session:', sessionResult);
              setQuizSession(sessionResult);

              // Transform randomized questions to match our Quiz interface
              const quizQuestions: QuizQuestion[] = sessionResult.questions.map((q) => {
                const options = Object.values(q.shuffledOptions);

                return {
                  ...q,
                  text: decodeHtmlEntities(q.question_text || 'Question text not available'),
                  options: options.map(option => decodeHtmlEntities(option)),
                  correctAnswer: q.shuffledCorrectIndex,
                  originalCorrectAnswer: q.correct_answer,
                  explanation: decodeHtmlEntities(q.explanation || ""),
                  is_premium: isPremiumTopic
                };
              });

              // Create quiz object with randomized questions
              const quizData: Quiz = {
                id: topicIdStr,
                title: topicData.title,
                description: topicData.description || "",
                is_premium: isPremiumTopic,
                questions: quizQuestions
              };

              console.log('Quiz data with randomized questions:', quizData);
              setQuiz(quizData);

              // Initialize userAnswers array with nulls
              setUserAnswers(new Array(quizQuestions.length).fill(null));
            } else {
              // For non-public topics, require authentication
              toast({
                title: "Authentication Required",
                description: "Please sign in to access this quiz",
                variant: "destructive",
              });
              navigate("/quizzes");
              return;
            }
          } else {
            // For authenticated users, use the normal flow
            // Check if there are sufficient questions for the topic
            const stats = await QuizRandomizationService.getTopicQuestionStats(topicIdStr);
            console.log('Topic question stats:', stats);

            if (stats.total_questions === 0) {
              toast({
                title: "No Questions Available",
                description: "This topic doesn't have any questions yet. Please check back later.",
                variant: "destructive",
              });
              navigate("/quizzes");
              return;
            }

            // Get quiz length from URL parameter or user preference
            const lengthParam = searchParams.get('length');
            let desiredQuizLength = 15; // Default quiz length
            
            if (lengthParam) {
              const parsedLength = parseInt(lengthParam);
              if (!isNaN(parsedLength) && parsedLength >= 1 && parsedLength <= 50) {
                desiredQuizLength = parsedLength;
              }
            } else {
              // Use user's default preference if no parameter provided
              desiredQuizLength = await UserPreferencesService.getDefaultQuizLength(user);
            }
            
            const actualQuizLength = Math.min(desiredQuizLength, stats.total_questions);

            if (actualQuizLength < desiredQuizLength) {
              toast({
                title: "Limited Questions",
                description: `This topic has only ${stats.total_questions} questions. Quiz will be ${actualQuizLength} questions long.`,
                variant: "default",
              });
            }

            // Generate the randomized quiz session
            const sessionResult = await QuizRandomizationService.generateQuizSession(
              topicIdStr,
              user.id,
              actualQuizLength
            );

            console.log('Generated quiz session:', sessionResult);
            setQuizSession(sessionResult);

            // Transform randomized questions to match our Quiz interface
            const quizQuestions: QuizQuestion[] = sessionResult.questions.map((q) => {
              const options = Object.values(q.shuffledOptions);

              return {
                ...q,
                text: decodeHtmlEntities(q.question_text || 'Question text not available'),
                options: options.map(option => decodeHtmlEntities(option)),
                correctAnswer: q.shuffledCorrectIndex,
                originalCorrectAnswer: q.correct_answer,
                explanation: decodeHtmlEntities(q.explanation || ""),
                is_premium: isPremiumTopic
              };
            });

            // Create quiz object with randomized questions
            const quizData: Quiz = {
              id: topicIdStr,
              title: topicData.title,
              description: topicData.description || "",
              is_premium: isPremiumTopic,
              questions: quizQuestions
            };

            console.log('Quiz data with randomized questions:', quizData);
            setQuiz(quizData);

            // Initialize userAnswers array with nulls
            setUserAnswers(new Array(quizQuestions.length).fill(null));
          }

        } catch (sessionError) {
          console.error('Error generating quiz session:', sessionError);

          // Try fallback simple quiz generation
          try {
            console.log('Attempting fallback quiz generation...');
            
            // Simple fallback: get questions directly without randomization service
            const { data: fallbackQuestions, error: fallbackError } = await supabase
              .from('questions')
              .select('*')
              .eq('topic_id', topicIdStr)
              .limit(15);

            if (fallbackError) {
              throw fallbackError;
            }

            if (!fallbackQuestions || fallbackQuestions.length === 0) {
              throw new Error('No questions available for this topic');
            }

            console.log('Fallback questions loaded:', fallbackQuestions.length);

            // Create simple quiz without randomization service
            const simpleQuizQuestions: QuizQuestion[] = fallbackQuestions.map((q, index) => {
              const options = parseQuestionOptions(q.options);
              const parsedAnswer = parseCorrectAnswer(q.correct_answer, options.length);

              return {
                ...q,
                text: decodeHtmlEntities(q.question_text || 'Question text not available'),
                options: options.map(option => decodeHtmlEntities(option)),
                correctAnswer: parsedAnswer.correctIndex,
                originalCorrectAnswer: q.correct_answer,
                explanation: decodeHtmlEntities(q.explanation || ""),
                is_premium: isPremiumTopic,
                // Simple randomization fields
                originalCorrectIndex: parsedAnswer.correctIndex,
                shuffledCorrectIndex: parsedAnswer.correctIndex,
                optionMapping: Array.from({ length: options.length }, (_, i) => i),
                shuffledOptions: q.options as Record<string, string>
              };
            });

            // Create simple quiz object
            const fallbackQuiz: Quiz = {
              id: topicIdStr,
              title: topicData.title,
              description: topicData.description || "",
              is_premium: isPremiumTopic,
              questions: simpleQuizQuestions
            };

            console.log('Fallback quiz created successfully');
            setQuiz(fallbackQuiz);
            setUserAnswers(new Array(simpleQuizQuestions.length).fill(null));

            toast({
              title: "Quiz Loaded",
              description: "Quiz loaded in simplified mode.",
              variant: "default",
            });

            return; // Success with fallback

          } catch (fallbackError) {
            console.error('Fallback quiz generation also failed:', fallbackError);
          }

          // If both methods fail, show error
          if (sessionError instanceof Error) {
            if (sessionError.message.includes('No questions available') || sessionError.message.includes('total_questions')) {
              toast({
                title: "No Questions Available",
                description: "This topic doesn't have any questions yet. Please check back later.",
                variant: "destructive",
              });
            } else if (sessionError.message.includes('Topic ID is required') || sessionError.message.includes('User ID is required')) {
              toast({
                title: "Invalid Request",
                description: "Missing required information. Please try again.",
                variant: "destructive",
              });
            } else if (sessionError.message.includes('timeout')) {
              toast({
                title: "Request Timeout",
                description: "The request took too long. Please try again.",
                variant: "destructive",
              });
            } else {
              toast({
                title: "Quiz Generation Failed",
                description: "Unable to generate quiz questions. Please try again or contact support.",
                variant: "destructive",
              });
            }
          } else {
            toast({
              title: "Unknown Error",
              description: "An unexpected error occurred. Please try again.",
              variant: "destructive",
            });
          }

          setSessionError(sessionError instanceof Error ? sessionError.message : 'Unknown error');
          navigate("/quizzes");
          return;
        }

        // Fetch learning materials for this topic
        try {
          const materials = await fetchLearningMaterialsByTopic(topicIdStr);
          setLearningMaterials(materials);
          console.log('Learning materials found for this topic:', materials.length);
        } catch (materialError) {
          console.error("Error loading learning materials:", materialError);
        }
      } catch (error) {
        console.error("Error loading quiz:", error);
        toast({
          title: "Error",
          description: "Failed to load quiz data",
          variant: "destructive",
        });
        navigate("/quizzes");
      } finally {
        setLoading(false);
      }
    };

    loadQuiz();
  }, [topicId, user, toast, navigate]);

  // Check for existing session and show resume dialog
  useEffect(() => {
    if (!topicId || !quiz) return;

    if (sessionPersistence.canResumeSession(topicId)) {
      setShowResumeDialog(true);
    }
  }, [topicId, quiz, sessionPersistence]);

  // Save session state when quiz state changes
  useEffect(() => {
    if (!quiz || !quizSession || quizCompleted) return;

    const sessionState = {
      sessionId: quizSession.sessionId,
      topicId: topicId || '',
      currentQuestionIndex,
      selectedAnswers: userAnswers.reduce((acc, answer, index) => {
        if (answer !== null) acc[index] = answer;
        return acc;
      }, {} as Record<number, number>),
      timeRemaining: timeLeft,
      startTime: startTimeRef.current.getTime(),
      questions: quiz.questions,
      isCompleted: quizCompleted,
      score,
      totalQuestions: quiz.questions.length,
    };

    sessionPersistence.updateSessionState(sessionState);
  }, [currentQuestionIndex, userAnswers, timeLeft, score, quizCompleted, quiz, quizSession, topicId, sessionPersistence]);

  // Get free questions used from localStorage
  useEffect(() => {
    // For non-premium users and guests
    if (user && !isPremiumUser(user) && quiz && quiz.is_premium) {
      // Only apply free question limit for premium topics
      const usedQuestions = localStorage.getItem('freeQuestionsUsed');
      if (usedQuestions) {
        const count = parseInt(usedQuestions);
        setFreeQuestionsUsed(count);
        if (count >= 10) {
          setShowFreeLimit(true);
        }
      }
    }
  }, [user, quiz]);

  // Set the selected option when in review mode
  useEffect(() => {
    if (reviewMode && quiz) {
      setSelectedOption(userAnswers[currentQuestionIndex]);
    }
  }, [currentQuestionIndex, reviewMode, userAnswers, quiz]);

  // Timer effect
  useEffect(() => {
    if (!quiz || quizCompleted || showAnswer || showFreeLimit || reviewMode) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setShowAnswer(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [quiz, quizCompleted, showAnswer, showFreeLimit, reviewMode]);

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 text-cyber-primary animate-spin mb-4" />
            <p className="text-muted-foreground">Loading quiz...</p>
          </div>
        </div>
      </div>
    );
  }

  // If session error occurred
  if (sessionError) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>
          <h1 className="text-2xl font-bold mb-2">Quiz Session Error</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            {sessionError}
          </p>
          <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
            <Link to="/quizzes">Back to Quizzes</Link>
          </Button>
        </div>
      </div>
    );
  }

  // If no quiz data after loading
  if (featureFlags.debug) {
    console.log('Quiz data check:', quiz);
  }
  if (!quiz || !quiz.questions || quiz.questions.length === 0) {
    // Show coupon input and upgrade button for premium topics if access was denied
    if (showCouponInput) {
      return (
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
            <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
              <AlertCircle className="w-10 h-10 text-cyber-warning" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Access Required</h1>
            <p className="text-center text-muted-foreground mb-8 max-w-md">
              This is a premium quiz. Please upgrade to premium or use a coupon code to access.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 w-full max-w-md items-start justify-center">
              <div className="flex-1 w-full">
                <Input
                  placeholder="Enter coupon code"
                  value={couponCode}
                  onChange={e => setCouponCode(e.target.value)}
                  className="max-w-xs mb-2"
                />
                <Button
                  onClick={() => {
                    // Accept any valid coupon for any premium topic
                    if (couponCode.trim() === "FOUNDATION2025" || couponCode.trim() === "GRC0125") {
                      localStorage.setItem(`coupon_${user?.id}_${quiz ? quiz.title : ''}`, "true");
                      setCouponError("");
                      toast({ title: "Coupon applied!", description: "You now have access to this premium quiz." });
                      setShowCouponInput(false);
                      window.location.reload();
                    } else {
                      setCouponError("Invalid coupon code");
                    }
                  }}
                  className="w-full"
                >
                  Redeem Coupon
                </Button>
                {couponError && <div className="text-red-500 text-sm mt-1">{couponError}</div>}
              </div>
              <div className="flex flex-col gap-2 w-full">
                <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90 w-full">
                  <Link to="/#pricing">Upgrade to Premium</Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link to="/quizzes">Back to Quizzes</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>

          <h1 className="text-2xl font-bold mb-2">Quiz Not Found</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            Sorry, we couldn't find the quiz you're looking for. Please try another topic.
          </p>

          <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
            <Link to="/quizzes">Browse Quizzes</Link>
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quiz.questions.length) * 100;

  const handleOptionSelect = (index: number) => {
    if (showAnswer || reviewMode) return;
    setSelectedOption(index);
  };

  const handleSubmitAnswer = () => {
    if (selectedOption === null) {
      toast({
        title: "No option selected",
        description: "Please select an answer before continuing",
        variant: "destructive",
      });
      return;
    }
    setShowAnswer(true);
  };

  const handleNext = async () => {
    // Store the user's answer for this question
    const newUserAnswers = [...userAnswers];
    if (
      typeof currentQuestionIndex === "number" &&
      currentQuestionIndex >= 0 &&
      currentQuestionIndex < newUserAnswers.length
    ) {
      newUserAnswers[currentQuestionIndex] = selectedOption;
    }
    setUserAnswers(newUserAnswers);

    // Use enhanced answer validation with standardized format support
    const validationResult = validateAnswer(
      selectedOption,
      currentQuestion.originalCorrectAnswer,
      4 // Standard 4 options (A, B, C, D)
    );

    const isCorrect = validationResult.isCorrect;

    // Log validation details for debugging
    logAnswerValidation(
      currentQuestion.id,
      currentQuestion.originalCorrectAnswer,
      selectedOption,
      validationResult
    );

    // Record question analytics
    if (user && quizSession) {
      const timeToAnswer = 60 - timeLeft; // Calculate time taken
      await recordQuestionAnalytics(
        currentQuestion.id,
        isCorrect,
        selectedOption || -1,
        timeToAnswer
      );
    }

    // Check if answer is correct and update score
    if (isCorrect) {
      setScore(score + 1);
    }

    // For non-premium users, track free questions used
    if (!isPremiumUser(user)) {
      // Only apply free question limit for premium topics
      // Free topics (PUBLIC_TOPICS and AUTHENTICATED_TOPICS) don't have a question limit for registered users
      if (quiz.is_premium &&
        !PUBLIC_TOPICS.includes(quiz.title) &&
        !AUTHENTICATED_TOPICS.includes(quiz.title)) {
        const newCount = freeQuestionsUsed + 1;
        localStorage.setItem('freeQuestionsUsed', newCount.toString());
        setFreeQuestionsUsed(newCount);

        // Check if we've hit the limit
        if (newCount >= 10 && currentQuestionIndex < quiz.questions.length - 1) {
          setShowFreeLimit(true);
          return;
        }
      }
    }

    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedOption(null);
      setShowAnswer(false);
      setTimeLeft(60);
    } else {
      // Calculate final score
      const finalScore = score + (isCorrect ? 1 : 0);
      setQuizCompleted(true);

      // Complete the session
      sessionPersistence.completeSession(finalScore);

      // Save quiz attempt to database if user is logged in
      if (user) {
        const endTime = new Date();
        const timeTaken = Math.round((endTime.getTime() - startTimeRef.current.getTime()) / 1000); // in seconds

        saveQuizAttempt(finalScore, quiz.questions.length, timeTaken);
      }

      // Track quiz completion analytics
      const endTime = new Date();
      const timeTaken = Math.round((endTime.getTime() - startTimeRef.current.getTime()) / 1000);
      const scorePercentage = Math.round((finalScore / quiz.questions.length) * 100);

      QuizAnalyticsService.trackQuizCompletion(
        user,
        topicId || '',
        quiz.questions.length,
        scorePercentage,
        timeTaken
      );

      toast({
        title: "Quiz completed!",
        description: `You scored ${finalScore} out of ${quiz.questions.length}`,
      });
    }
  };

  // Free limit reached screen
  if (showFreeLimit) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>

          <h1 className="text-2xl font-bold mb-2">Free Limit Reached</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            You've reached the limit of free questions for premium content. Upgrade to premium to continue and access all premium content.
          </p>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            Note: All registered users have unlimited access to our three free courses: "Cybersecurity Foundation - Easy", "CIA Triad", and "ISC2 Certification".
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild variant="outline">
              <Link to="/quizzes">Browse Quizzes</Link>
            </Button>
            <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
              <Link to="/#pricing">Upgrade to Premium</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Results screen
  if (quizCompleted) {
    const finalScore = score;
    // Ensure percentage doesn't exceed 100%
    const percentage = Math.min(100, Math.round((finalScore / quiz.questions.length) * 100));

    return (
      <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
        <Navbar />
        <div className="flex-1 container py-8 px-4">
          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 mb-6">
              <div className="flex flex-col items-center mb-6">
                <div className="w-24 h-24 rounded-full bg-cyber-success/20 flex items-center justify-center mb-4">
                  <Trophy className="w-12 h-12 text-cyber-success" />
                </div>
                <h1 className="text-2xl font-bold mb-2">Quiz Completed!</h1>
                <p className="text-center text-muted-foreground mb-4">
                  You've completed the {quiz.title} quiz.
                </p>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-3xl font-bold">{finalScore}</span>
                  <span className="text-muted-foreground">/ {quiz.questions.length}</span>
                </div>
                <div className="w-full max-w-md mb-4">
                  <Progress value={percentage} className="h-3" />
                </div>
                <div className={`px-2 py-1 rounded text-sm font-medium ${percentage >= 70 ? 'bg-green-100 text-green-800' : percentage >= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                  {percentage}% Score
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="flex-1 sm:flex-initial"
                  onClick={() => {
                    setReviewMode(true);
                    setCurrentQuestionIndex(0);
                    setQuizCompleted(false);
                  }}
                >
                  <BookOpen className="h-4 w-4 mr-2" /> Review Answers
                </Button>
                <Button
                  asChild
                  className="flex-1 sm:flex-initial bg-cyber-primary hover:bg-cyber-primary/90"
                >
                  <Link to="/quizzes">More Quizzes</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  // Review mode or normal quiz mode
  return (
    <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
      <Navbar />

      {/* Resume Quiz Dialog */}
      <ResumeQuizDialog
        open={showResumeDialog}
        onOpenChange={setShowResumeDialog}
        onResume={resumeSession}
        onStartNew={startNewSession}
        topicTitle={quiz?.title}
      />
      <div className="flex-1 container py-8 px-4">
        <div className="max-w-3xl mx-auto">
          {/* Quiz header */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h1 className="text-xl font-bold">{quiz.title}</h1>
              <div className="flex items-center gap-2">
                {reviewMode && (
                  <Badge variant="outline" className="mr-2">Review Mode</Badge>
                )}
                <div className={`px-2 py-1 rounded text-sm font-medium ${quiz.is_premium ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                  {quiz.is_premium ? "Premium" : "Free"}
                </div>

                {/* Learning materials button */}
                {learningMaterials.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() => setShowLearningMaterials(!showLearningMaterials)}
                  >
                    <GraduationCap className="h-4 w-4" />
                    <span>{showLearningMaterials ? "Hide Materials" : "Study Materials"}</span>
                  </Button>
                )}
              </div>
            </div>
            <p className="text-muted-foreground text-sm mb-4">{quiz.description}</p>

            {/* Learning materials section */}
            {showLearningMaterials && learningMaterials.length > 0 && (
              <div className="mb-6 bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg border border-indigo-100 dark:border-indigo-800">
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-indigo-600 dark:text-indigo-400" />
                  <span>Learning Materials</span>
                </h3>
                <div className="space-y-3">
                  {learningMaterials.map((material) => (
                    <div key={material.id} className="bg-white dark:bg-indigo-800/50 p-3 rounded border border-indigo-200 dark:border-indigo-700">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-indigo-900 dark:text-indigo-100 mb-1">
                            {material.title}
                          </h4>
                          {material.summary && (
                            <p className="text-sm text-indigo-700 dark:text-indigo-300 mb-2">
                              {material.summary}
                            </p>
                          )}
                          <div className="flex items-center gap-2 text-xs text-indigo-600 dark:text-indigo-400">
                            <span className="px-2 py-1 bg-indigo-100 dark:bg-indigo-700 rounded">
                              Learning Material
                            </span>
                            {material.is_premium && (
                              <span className="px-2 py-1 bg-purple-100 dark:bg-purple-700 rounded text-purple-800 dark:text-purple-200">
                                Premium
                              </span>
                            )}
                          </div>
                        </div>
                        <Button
                          asChild
                          size="sm"
                          variant="outline"
                          className="ml-3 border-indigo-300 text-indigo-700 hover:bg-indigo-50 dark:border-indigo-600 dark:text-indigo-300 dark:hover:bg-indigo-800"
                        >
                          <Link to={`/learn/${material.id}`}>
                            View
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Progress and timer */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  Question {currentQuestionIndex + 1} of {quiz.questions.length}
                </span>
              </div>
              {!reviewMode && (
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-muted-foreground" />
                  <span className={`text-sm font-medium ${timeLeft <= 10 ? 'text-red-500' : 'text-muted-foreground'}`}>
                    {timeLeft}s
                  </span>
                </div>
              )}
            </div>
            <Progress value={progress} className="mb-6" />
          </div>

          {/* Question card */}
          <Card className="p-6 mb-6">
            <h2 className="text-lg font-medium mb-6">{currentQuestion.text}</h2>

            {/* Answer options */}
            <div className="space-y-3 mb-6">
              {currentQuestion.options.map((option, index) => (
                <div key={index}>
                  <div
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${selectedOption === index
                        ? 'border-cyber-primary bg-cyber-primary/10'
                        : 'border-gray-200 hover:border-gray-300'
                      } ${showAnswer && index === currentQuestion.correctAnswer ? 'bg-green-500 text-white' : ''
                      } ${showAnswer && selectedOption === index && index !== currentQuestion.correctAnswer ? 'bg-red-500 text-white' : ''
                      }`}
                    onClick={() => handleOptionSelect(index)}
                  >
                    <div className="flex items-center">
                      <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3 text-sm font-medium ${selectedOption === index
                          ? 'border-cyber-primary bg-cyber-primary text-white'
                          : 'border-gray-300'
                        } ${showAnswer && index === currentQuestion.correctAnswer ? 'bg-green-500 text-white' : ''
                        } ${showAnswer && selectedOption === index && index !== currentQuestion.correctAnswer ? 'bg-red-500 text-white' : ''
                        }`}>
                        {String.fromCharCode(65 + index)}
                      </div>
                      <span className="flex-1">{option}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Explanation (shown after answering) */}
            {showAnswer && currentQuestion.explanation && (
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start">
                  <LightbulbIcon className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-1">Explanation</h3>
                    <p className="text-sm text-blue-600 dark:text-blue-400">{currentQuestion.explanation}</p>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Action buttons */}
          <div className="flex justify-between">
            {reviewMode ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    if (currentQuestionIndex > 0) {
                      setCurrentQuestionIndex(currentQuestionIndex - 1);
                    }
                  }}
                  disabled={currentQuestionIndex === 0}
                  className="flex items-center gap-1"
                >
                  <ChevronLeft className="h-4 w-4" /> Previous
                </Button>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setReviewMode(false);
                      setQuizCompleted(true);
                    }}
                  >
                    Back to Results
                  </Button>
                  <Button
                    onClick={() => {
                      if (currentQuestionIndex < quiz.questions.length - 1) {
                        setCurrentQuestionIndex(currentQuestionIndex + 1);
                      }
                    }}
                    disabled={currentQuestionIndex === quiz.questions.length - 1}
                    className="flex items-center gap-1"
                  >
                    Next <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </>
            ) : (
              <>
                {!showAnswer ? (
                  <Button
                    className="ml-auto bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleSubmitAnswer}
                  >
                    Submit Answer
                  </Button>
                ) : (
                  <Button
                    className="ml-auto bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleNext}
                  >
                    {currentQuestionIndex < quiz.questions.length - 1 ? "Next Question" : "See Results"}
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};

export default QuizPage;