/**
 * Robust answer parsing utility for quiz questions
 * Handles various data formats and provides comprehensive error handling
 */

export interface ParsedAnswer {
  correctIndex: number;
  isValid: boolean;
  originalValue: any;
  errorMessage?: string;
}

export interface AnswerValidationResult {
  isCorrect: boolean;
  selectedIndex: number;
  correctIndex: number;
  confidence: 'high' | 'medium' | 'low';
  warnings: string[];
}

/**
 * Converts letter format (A,B,C,D) to index format (0,1,2,3)
 * Used for standardized answer format after database migration
 * @param letter - The letter answer (A, B, C, or D)
 * @returns The corresponding index (0, 1, 2, or 3) or -1 if invalid
 */
export function letterToIndex(letter: string): number {
  const upperLetter = letter.trim().toUpperCase();
  switch (upperLetter) {
    case 'A': return 0;
    case 'B': return 1;
    case 'C': return 2;
    case 'D': return 3;
    default: return -1;
  }
}

/**
 * Converts index format (0,1,2,3) to letter format (A,B,C,D)
 * Used for displaying standardized answers
 * @param index - The index (0, 1, 2, or 3)
 * @returns The corresponding letter (A, B, C, or D) or 'A' if invalid
 */
export function indexToLetter(index: number): string {
  switch (index) {
    case 0: return 'A';
    case 1: return 'B';
    case 2: return 'C';
    case 3: return 'D';
    default: return 'A'; // Default fallback
  }
}

/**
 * Parses the correct answer from various formats with robust error handling
 * UPDATED: Now prioritizes standardized letter format (A,B,C,D) after database migration
 * @param correctAnswer - The correct answer value from database (string, number, or other)
 * @param optionsCount - Number of available options for validation
 * @returns ParsedAnswer object with validation results
 */
export function parseCorrectAnswer(correctAnswer: any, optionsCount: number = 4): ParsedAnswer {
  const result: ParsedAnswer = {
    correctIndex: 0,
    isValid: false,
    originalValue: correctAnswer
  };

  try {
    // Handle null or undefined
    if (correctAnswer === null || correctAnswer === undefined) {
      result.errorMessage = 'Correct answer is null or undefined';
      console.warn('Answer validation: Correct answer is null/undefined, defaulting to 0');
      return result;
    }

    // Handle string type (prioritized for standardized format)
    if (typeof correctAnswer === 'string') {
      const trimmed = correctAnswer.trim().toUpperCase();

      // Handle empty string
      if (trimmed === '') {
        result.errorMessage = 'Correct answer is empty string';
        console.warn('Answer validation: Empty string, defaulting to 0');
        return result;
      }

      // PRIORITY: Handle standardized letter format (A, B, C, D)
      if (trimmed === 'A') {
        result.correctIndex = 0;
        result.isValid = true;
        return result;
      }
      if (trimmed === 'B') {
        result.correctIndex = 1;
        result.isValid = true;
        return result;
      }
      if (trimmed === 'C') {
        result.correctIndex = 2;
        result.isValid = true;
        return result;
      }
      if (trimmed === 'D') {
        result.correctIndex = 3;
        result.isValid = true;
        return result;
      }

      // FALLBACK: Handle legacy numeric string format (0, 1, 2, 3)
      const parsed = parseInt(trimmed, 10);
      if (!isNaN(parsed) && parsed >= 0 && parsed < optionsCount) {
        result.correctIndex = parsed;
        result.isValid = true;
        console.warn(`Answer validation: Using legacy numeric format "${trimmed}", consider migrating to letter format`);
        return result;
      }

      // Invalid string format
      result.errorMessage = `Invalid answer format "${trimmed}". Expected A, B, C, D or 0, 1, 2, 3`;
      console.warn(`Answer validation: Invalid format "${trimmed}", defaulting to 0`);
      return result;
    }

    // Handle number type (legacy support)
    if (typeof correctAnswer === 'number') {
      if (isNaN(correctAnswer) || !isFinite(correctAnswer)) {
        result.errorMessage = 'Correct answer is NaN or infinite';
        console.warn('Answer validation: Correct answer is NaN or infinite, defaulting to 0');
        return result;
      }

      const index = Math.floor(correctAnswer);
      if (index >= 0 && index < optionsCount) {
        result.correctIndex = index;
        result.isValid = true;
        console.warn(`Answer validation: Using legacy numeric format ${index}, consider migrating to letter format`);
        return result;
      } else {
        result.errorMessage = `Correct answer index ${index} is out of range (0-${optionsCount - 1})`;
        result.correctIndex = Math.max(0, Math.min(optionsCount - 1, index));
        console.warn(`Answer validation: Index ${index} out of range, clamping to ${result.correctIndex}`);
        return result;
      }
    }

    // Handle boolean type (treat as 0 or 1)
    if (typeof correctAnswer === 'boolean') {
      const index = correctAnswer ? 1 : 0;
      if (index < optionsCount) {
        result.correctIndex = index;
        result.isValid = true;
        return result;
      } else {
        result.errorMessage = `Boolean value ${correctAnswer} converted to index ${index} exceeds options count`;
        console.warn(`Answer validation: Boolean index ${index} exceeds options, defaulting to 0`);
        return result;
      }
    }

    // Handle other types
    result.errorMessage = `Unsupported correct answer type: ${typeof correctAnswer}`;
    console.warn(`Answer validation: Unsupported type ${typeof correctAnswer}, defaulting to 0`);
    return result;

  } catch (error) {
    result.errorMessage = `Error parsing correct answer: ${error instanceof Error ? error.message : 'Unknown error'}`;
    console.error('Answer validation: Parsing error:', error);
    return result;
  }
}

/**
 * Validates a user's answer selection against the correct answer
 * @param selectedIndex - The index of the option selected by the user
 * @param correctAnswer - The correct answer value from database
 * @param optionsCount - Number of available options
 * @returns AnswerValidationResult with detailed validation information
 */
export function validateAnswer(
  selectedIndex: number | null,
  correctAnswer: any,
  optionsCount: number = 4
): AnswerValidationResult {
  const warnings: string[] = [];
  
  // Parse the correct answer
  const parsedAnswer = parseCorrectAnswer(correctAnswer, optionsCount);
  
  if (!parsedAnswer.isValid) {
    warnings.push(parsedAnswer.errorMessage || 'Failed to parse correct answer');
  }

  // Validate selected index
  if (selectedIndex === null || selectedIndex === undefined) {
    return {
      isCorrect: false,
      selectedIndex: -1,
      correctIndex: parsedAnswer.correctIndex,
      confidence: 'high',
      warnings: [...warnings, 'No answer selected']
    };
  }

  if (typeof selectedIndex !== 'number' || isNaN(selectedIndex)) {
    warnings.push('Selected index is not a valid number');
    return {
      isCorrect: false,
      selectedIndex: -1,
      correctIndex: parsedAnswer.correctIndex,
      confidence: 'low',
      warnings
    };
  }

  if (selectedIndex < 0 || selectedIndex >= optionsCount) {
    warnings.push(`Selected index ${selectedIndex} is out of range (0-${optionsCount - 1})`);
    return {
      isCorrect: false,
      selectedIndex,
      correctIndex: parsedAnswer.correctIndex,
      confidence: 'low',
      warnings
    };
  }

  // Determine confidence level
  let confidence: 'high' | 'medium' | 'low' = 'high';
  if (!parsedAnswer.isValid) {
    confidence = 'low';
  } else if (warnings.length > 0) {
    confidence = 'medium';
  }

  return {
    isCorrect: selectedIndex === parsedAnswer.correctIndex,
    selectedIndex,
    correctIndex: parsedAnswer.correctIndex,
    confidence,
    warnings
  };
}

/**
 * Parses question options from various formats
 * @param options - Options data from database (array, object, or string)
 * @returns Array of option strings
 */
export function parseQuestionOptions(options: any): string[] {
  try {
    // Handle null or undefined
    if (options === null || options === undefined) {
      console.warn('Options parsing: Options is null/undefined');
      return ['Option not available'];
    }

    // Handle array format
    if (Array.isArray(options)) {
      return options.map((opt, index) => {
        if (typeof opt === 'string') return opt;
        if (opt === null || opt === undefined) return `Option ${index + 1}`;
        return String(opt);
      });
    }

    // Handle object format with numeric keys
    if (typeof options === 'object') {
      const keys = Object.keys(options).sort((a, b) => {
        const numA = parseInt(a, 10);
        const numB = parseInt(b, 10);
        if (isNaN(numA) || isNaN(numB)) return a.localeCompare(b);
        return numA - numB;
      });

      return keys.map(key => {
        const value = options[key];
        if (typeof value === 'string') return value;
        if (value === null || value === undefined) return `Option ${key}`;
        return String(value);
      });
    }

    // Handle string format (try to parse as JSON)
    if (typeof options === 'string') {
      try {
        const parsed = JSON.parse(options);
        return parseQuestionOptions(parsed); // Recursive call with parsed data
      } catch {
        // If JSON parsing fails, treat as single option
        console.warn('Options parsing: Failed to parse string as JSON, treating as single option');
        return [options];
      }
    }

    // Fallback for other types
    console.warn(`Options parsing: Unsupported options type: ${typeof options}`);
    return ['Option not available'];

  } catch (error) {
    console.error('Options parsing: Error parsing options:', error);
    return ['Option not available'];
  }
}

/**
 * Logs comprehensive answer validation information for debugging
 * @param questionId - ID of the question
 * @param correctAnswer - Original correct answer value
 * @param selectedIndex - User's selected index
 * @param validationResult - Result from validateAnswer function
 */
export function logAnswerValidation(
  questionId: string,
  correctAnswer: any,
  selectedIndex: number | null,
  validationResult: AnswerValidationResult
): void {
  const logData = {
    questionId,
    originalCorrectAnswer: correctAnswer,
    selectedIndex,
    validationResult,
    timestamp: new Date().toISOString()
  };

  if (validationResult.warnings.length > 0 || validationResult.confidence !== 'high') {
    console.warn('Answer validation issues detected:', logData);
  } else {
    console.log('Answer validation successful:', logData);
  }

  // In production, you might want to send this to a logging service
  // logToService(logData);
}

/**
 * Provides a safe fallback for answer validation with comprehensive error handling
 * @param selectedIndex - User's selected answer index
 * @param originalCorrectAnswer - Original correct answer from database
 * @param parsedCorrectIndex - Pre-parsed correct answer index
 * @param optionsCount - Number of available options
 * @returns Safe validation result with fallback handling
 */
export function safeValidateAnswer(
  selectedIndex: number | null,
  originalCorrectAnswer: any,
  parsedCorrectIndex: number,
  optionsCount: number
): AnswerValidationResult {
  try {
    // First try the robust validation
    const validationResult = validateAnswer(selectedIndex, originalCorrectAnswer, optionsCount);
    
    // If validation succeeded, return the result
    if (validationResult.confidence === 'high') {
      return validationResult;
    }

    // If validation had issues, fall back to simple index comparison
    console.warn('Falling back to simple index comparison due to validation issues');
    
    if (selectedIndex === null || selectedIndex === undefined) {
      return {
        isCorrect: false,
        selectedIndex: -1,
        correctIndex: parsedCorrectIndex,
        confidence: 'medium',
        warnings: ['No answer selected', 'Using fallback validation']
      };
    }

    return {
      isCorrect: selectedIndex === parsedCorrectIndex,
      selectedIndex,
      correctIndex: parsedCorrectIndex,
      confidence: 'medium',
      warnings: ['Using fallback validation due to parsing issues']
    };

  } catch (error) {
    console.error('Critical error in answer validation, using emergency fallback:', error);
    
    // Emergency fallback - just compare indices
    return {
      isCorrect: selectedIndex === parsedCorrectIndex,
      selectedIndex: selectedIndex || -1,
      correctIndex: parsedCorrectIndex,
      confidence: 'low',
      warnings: ['Emergency fallback validation used due to critical error']
    };
  }
}