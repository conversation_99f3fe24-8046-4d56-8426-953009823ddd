import { supabase } from "@/integrations/supabase/client";
import { isTopicPremium } from "./topic-access";

export interface TopicWithCount {
  id: string;
  title: string;
  description: string | null;
  icon: string | null;
  difficulty: string | null;
  is_active: boolean;
  is_premium: boolean;
  question_count: number;
  domain_id?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface QuizTopicForUI {
  id: string;
  title: string;
  description: string;
  questionCount: number;
  points: number;
  difficulty: "easy" | "medium" | "hard";
  isPremium: boolean;
  icon?: string;
  category: string;
}

/**
 * Fetches all active topics from the database with question counts
 */
export async function fetchAllTopics(): Promise<TopicWithCount[]> {
  try {
    // Fetch topics from Supabase
    const { data, error } = await supabase
      .from("topics")
      .select("*, domain_id")
      .eq("is_active", true)
      .order("title");

    if (error) throw error;

    if (!data || data.length === 0) {
      console.log('No topics found in database');
      return [];
    }

    // Get question counts for each topic
    const topicsWithCounts = await Promise.all(data.map(async (topic) => {
      // Count questions for this topic
      const { count, error: countError } = await supabase
        .from('questions')
        .select('id', { count: 'exact' })
        .eq('topic_id', topic.id);

      if (countError) {
        console.error(`Error counting questions for topic ${topic.id}:`, countError);
        return {
          ...topic,
          question_count: 0,
          is_premium: isTopicPremium(topic.title, topic.difficulty)
        };
      }

      return {
        ...topic,
        question_count: count || 0,
        is_premium: isTopicPremium(topic.title, topic.difficulty)
      };
    }));

    return topicsWithCounts;
  } catch (error) {
    console.error("Error fetching topics:", error);
    return [];
  }
}

/**
 * Converts database topics to the format expected by the UI components
 */
export function convertToUIFormat(topics: TopicWithCount[]): QuizTopicForUI[] {
  return topics.map(topic => ({
    id: topic.id,
    title: topic.title,
    description: topic.description || "",
    questionCount: topic.question_count,
    points: 100, // Default points
    difficulty: (topic.difficulty as "easy" | "medium" | "hard") || "medium",
    isPremium: topic.is_premium,
    category: "Cybersecurity", // Default category
    icon: topic.icon || "shield"
  }));
}

/**
 * Fetches topics with questions for the homepage
 * Returns popular topics and featured topics
 */
export async function fetchHomePageTopics(): Promise<{
  popularTopics: QuizTopicForUI[];
  featuredTopics: QuizTopicForUI[];
}> {
  try {
    const allTopics = await fetchAllTopics();

    // Filter topics with at least 1 question
    const validTopics = allTopics.filter(topic => topic.question_count > 0);

    // Convert to UI format
    const uiTopics = convertToUIFormat(validTopics);

    // EXCLUDE "CISSP Prep" from homepage display
    const filteredTopics = uiTopics.filter(topic =>
      topic.title !== "CISSP Prep" &&
      topic.title !== "CISSP Preparation"
    );

    // PRIORITIZE "Recovery and Post-Incident Activities" for featured section
    const recoveryTopic = filteredTopics.find(topic =>
      topic.title === "Recovery and Post-Incident Activities"
    );

    // Sort by question count for popular topics (excluding CISSP Prep)
    const sortedByQuestionCount = [...filteredTopics].sort((a, b) => b.questionCount - a.questionCount);

    // Get top 3 for popular (excluding CISSP Prep)
    const popularTopics = sortedByQuestionCount.slice(0, 3);

    // Get 3 different topics for featured section
    let featured = [];

    // Always include Recovery topic first if available
    if (recoveryTopic) {
      featured.push(recoveryTopic);
    }

    // Fill remaining slots with other topics
    const remainingTopics = filteredTopics.filter(t =>
      t.id !== recoveryTopic?.id &&
      !popularTopics.some(p => p.id === t.id)
    );

    // Prioritize premium topics for remaining slots
    const premiumRemaining = remainingTopics.filter(t => t.isPremium);
    const nonPremiumRemaining = remainingTopics.filter(t => !t.isPremium);

    // Add premium topics first, then non-premium
    const additionalFeatured = [...premiumRemaining, ...nonPremiumRemaining];
    featured.push(...additionalFeatured.slice(0, 3 - featured.length));

    return {
      popularTopics,
      featuredTopics: featured.slice(0, 3)
    };
  } catch (error) {
    console.error("Error fetching homepage topics:", error);
    return {
      popularTopics: [],
      featuredTopics: []
    };
  }
}
