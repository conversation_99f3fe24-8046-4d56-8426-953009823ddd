-- Comprehensive Quiz Answer Format Standardization Script
-- This script fixes all answer validation issues by standardizing formats across all 700+ quizzes
-- Run this in Supabase SQL editor to fix the quiz answer validation problems

-- ============================================================================
-- PHASE 1: ANALYSIS - Understand current state
-- ============================================================================

-- Check current answer format distribution
SELECT 
  'ANSWER FORMAT ANALYSIS' as analysis_phase,
  CASE 
    WHEN correct_answer ~ '^[A-D]$' THEN 'LETTER_FORMAT'
    WHEN correct_answer ~ '^[0-3]$' THEN 'NUMERIC_FORMAT'
    WHEN correct_answer ~ '^[0-9]+$' THEN 'OTHER_NUMERIC'
    WHEN correct_answer IS NULL THEN 'NULL_VALUE'
    ELSE 'UNKNOWN_FORMAT'
  END as answer_format,
  COUNT(*) as question_count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM questions), 2) as percentage
FROM questions 
GROUP BY answer_format
ORDER BY question_count DESC;

-- Check options format distribution
SELECT 
  'OPTIONS FORMAT ANALYSIS' as analysis_phase,
  CASE 
    WHEN options ? 'A' AND options ? 'B' AND options ? 'C' AND options ? 'D' THEN 'LETTER_KEYS'
    WHEN options ? '0' AND options ? '1' AND options ? '2' AND options ? '3' THEN 'NUMERIC_KEYS'
    WHEN options IS NULL THEN 'NULL_OPTIONS'
    ELSE 'MIXED_OR_OTHER'
  END as options_format,
  COUNT(*) as question_count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM questions), 2) as percentage
FROM questions 
GROUP BY options_format
ORDER BY question_count DESC;

-- Find problematic questions (mismatched formats)
SELECT 
  'MISMATCH ANALYSIS' as analysis_phase,
  COUNT(*) as problematic_questions
FROM questions 
WHERE 
  (correct_answer ~ '^[0-3]$' AND options ? 'A') OR  -- Numeric answer with letter options
  (correct_answer ~ '^[A-D]$' AND options ? '0');    -- Letter answer with numeric options

-- ============================================================================
-- PHASE 2: BACKUP - Create backup before changes
-- ============================================================================

-- Create backup table
DROP TABLE IF EXISTS questions_backup_before_standardization;
CREATE TABLE questions_backup_before_standardization AS 
SELECT * FROM questions;

-- Verify backup
SELECT 
  'BACKUP VERIFICATION' as phase,
  COUNT(*) as backed_up_questions
FROM questions_backup_before_standardization;

-- ============================================================================
-- PHASE 3: STANDARDIZATION - Convert all to letter format (A,B,C,D)
-- ============================================================================

-- Step 1: Convert numeric options to letter options
UPDATE questions 
SET options = jsonb_build_object(
  'A', options->'0',
  'B', options->'1', 
  'C', options->'2',
  'D', options->'3'
)
WHERE options ? '0' AND options ? '1' AND options ? '2' AND options ? '3';

-- Step 2: Convert numeric correct_answer to letter format
UPDATE questions 
SET correct_answer = CASE 
  WHEN correct_answer = '0' THEN 'A'
  WHEN correct_answer = '1' THEN 'B'
  WHEN correct_answer = '2' THEN 'C'
  WHEN correct_answer = '3' THEN 'D'
  ELSE correct_answer  -- Keep existing if already in letter format
END
WHERE correct_answer ~ '^[0-3]$';

-- Step 3: Handle edge cases - questions with invalid correct_answer values
UPDATE questions 
SET correct_answer = 'A'
WHERE correct_answer IS NULL 
   OR correct_answer NOT IN ('A', 'B', 'C', 'D');

-- Step 4: Handle edge cases - questions with incomplete options
UPDATE questions 
SET options = jsonb_build_object(
  'A', COALESCE(options->>'A', options->>'0', 'Option A'),
  'B', COALESCE(options->>'B', options->>'1', 'Option B'),
  'C', COALESCE(options->>'C', options->>'2', 'Option C'),
  'D', COALESCE(options->>'D', options->>'3', 'Option D')
)
WHERE NOT (options ? 'A' AND options ? 'B' AND options ? 'C' AND options ? 'D');

-- ============================================================================
-- PHASE 4: VALIDATION - Verify all changes
-- ============================================================================

-- Verify answer format standardization
SELECT 
  'POST-STANDARDIZATION ANALYSIS' as phase,
  CASE 
    WHEN correct_answer ~ '^[A-D]$' THEN 'LETTER_FORMAT'
    WHEN correct_answer ~ '^[0-3]$' THEN 'NUMERIC_FORMAT'
    ELSE 'OTHER_FORMAT'
  END as answer_format,
  COUNT(*) as question_count
FROM questions 
GROUP BY answer_format;

-- Verify options format standardization
SELECT 
  'OPTIONS VERIFICATION' as phase,
  CASE 
    WHEN options ? 'A' AND options ? 'B' AND options ? 'C' AND options ? 'D' THEN 'STANDARDIZED'
    ELSE 'NEEDS_ATTENTION'
  END as options_status,
  COUNT(*) as question_count
FROM questions 
GROUP BY options_status;

-- Check for any remaining problematic questions
SELECT 
  'REMAINING ISSUES' as phase,
  COUNT(*) as problematic_count
FROM questions 
WHERE 
  correct_answer NOT IN ('A', 'B', 'C', 'D') OR
  NOT (options ? 'A' AND options ? 'B' AND options ? 'C' AND options ? 'D');

-- Sample verification - show converted questions
SELECT 
  'SAMPLE VERIFICATION' as phase,
  id,
  LEFT(question_text, 50) || '...' as question_preview,
  options,
  correct_answer
FROM questions 
WHERE correct_answer IN ('A', 'B', 'C', 'D')
  AND options ? 'A'
LIMIT 5;

-- ============================================================================
-- PHASE 5: CLEANUP AND DOCUMENTATION
-- ============================================================================

-- Add metadata to track this standardization
UPDATE questions 
SET updated_at = NOW()
WHERE id IN (
  SELECT id FROM questions_backup_before_standardization 
  WHERE 
    questions_backup_before_standardization.correct_answer != questions.correct_answer OR
    questions_backup_before_standardization.options != questions.options
);

-- Add comment to track this migration
COMMENT ON TABLE questions IS 'Quiz questions table - Answer format standardized to letter format (A,B,C,D) on ' || CURRENT_DATE || '. Backup available in questions_backup_before_standardization table.';

-- Final summary report
SELECT 
  'STANDARDIZATION COMPLETE' as status,
  (SELECT COUNT(*) FROM questions) as total_questions,
  (SELECT COUNT(*) FROM questions WHERE correct_answer IN ('A', 'B', 'C', 'D')) as standardized_answers,
  (SELECT COUNT(*) FROM questions WHERE options ? 'A' AND options ? 'B' AND options ? 'C' AND options ? 'D') as standardized_options,
  (SELECT COUNT(*) FROM questions_backup_before_standardization) as backup_count;

-- Show topics that were affected
SELECT 
  'AFFECTED TOPICS' as summary,
  t.title as topic_name,
  COUNT(q.id) as questions_updated
FROM topics t
INNER JOIN questions q ON t.id = q.topic_id
INNER JOIN questions_backup_before_standardization qb ON q.id = qb.id
WHERE qb.correct_answer != q.correct_answer OR qb.options != q.options
GROUP BY t.id, t.title
ORDER BY questions_updated DESC;
