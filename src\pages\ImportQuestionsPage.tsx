import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Spinner } from "@/components/ui/spinner";
import { 
  AlertCircle, 
  CheckCircle2, 
  Download, 
  Upload, 
  Info, 
  FileText, 
  Plus, 
  ArrowLeft,
  HelpCircle 
} from "lucide-react";
import { 
  parseQuestionCSVEnhanced, 
  generateCSVTemplate, 
  generateMultiTopicCSVTemplate,
  type MultiTopicImportResult,
  type ImportConfig,
  type Topic,
  type MultiTopicTemplateFormat
} from "@/utils/csv-import";
import { 
  batchImportService,
  type BatchImportProgress,
  type BatchImportResult,
  type BatchImportConfig
} from "@/services/batch-import-service";
import { useToast } from "@/hooks/use-toast";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { ImportErrorReporting, type ImportErrorReport } from "@/components/admin/ImportErrorReporting";
import { importErrorReportingService } from "@/services/import-error-reporting-service";
import { useAdminTopics } from "@/hooks/use-admin";
import Navbar from "@/components/Navbar";

interface ImportState {
  mode: 'single-topic' | 'multi-topic';
  selectedFile: File | null;
  selectedTopic: string;
  autoCreateTopics: boolean;
  previewData: MultiTopicImportResult | null;
  importing: boolean;
  showPreview: boolean;
  progress: number;
  progressMessage: string;
  batchResult: BatchImportResult | null;
  showHelp: boolean;
  errorReport: ImportErrorReport | null;
  currentStep: 'upload' | 'preview' | 'importing' | 'results';
}

export default function ImportQuestionsPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { data: topics = [], isLoading: topicsLoading } = useAdminTopics();

  const [state, setState] = useState<ImportState>({
    mode: 'single-topic',
    selectedFile: null,
    selectedTopic: "",
    autoCreateTopics: false,
    previewData: null,
    importing: false,
    showPreview: false,
    progress: 0,
    progressMessage: "",
    batchResult: null,
    showHelp: false,
    errorReport: null,
    currentStep: 'upload',
  });

  // Reset preview when file or configuration changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
      errorReport: null,
      currentStep: 'upload',
    }));
  }, [state.selectedFile, state.mode, state.selectedTopic, state.autoCreateTopics]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setState(prev => ({
        ...prev,
        selectedFile: file,
        previewData: null,
        showPreview: false,
        batchResult: null,
        progressMessage: "",
        currentStep: 'upload',
      }));
    }
  };

  const handleTopicChange = (value: string) => {
    setState(prev => ({
      ...prev,
      selectedTopic: value,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
      currentStep: 'upload',
    }));
  };

  const handleModeChange = (mode: 'single-topic' | 'multi-topic') => {
    setState(prev => ({
      ...prev,
      mode,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
      currentStep: 'upload',
    }));
  };

  const handleAutoCreateToggle = (checked: boolean) => {
    setState(prev => ({
      ...prev,
      autoCreateTopics: checked,
      previewData: null,
      showPreview: false,
      batchResult: null,
      progressMessage: "",
      currentStep: 'upload',
    }));
  };

  const handleDownloadTemplate = (templateType: 'single' | 'multi' = 'single', format?: MultiTopicTemplateFormat) => {
    let csvContent: string;
    let filename: string;
    
    if (templateType === 'multi') {
      csvContent = generateMultiTopicCSVTemplate(format);
      const formatSuffix = format === 'id-based' ? '_id_based' : format === 'both' ? '_both_columns' : '';
      filename = `multi_topic_questions_template${formatSuffix}.csv`;
    } else {
      csvContent = generateCSVTemplate();
      filename = "quiz_questions_template.csv";
    }
    
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePreview = async () => {
    if (!state.selectedFile) {
      toast({
        title: "Missing file",
        description: "Please select a CSV file to preview.",
        variant: "destructive",
      });
      return;
    }

    if (state.mode === 'single-topic' && !state.selectedTopic) {
      toast({
        title: "Missing topic",
        description: "Please select a topic for single-topic import.",
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ ...prev, importing: true, progress: 10, currentStep: 'preview' }));

    try {
      const config: ImportConfig = {
        mode: state.mode,
        autoCreateTopics: state.autoCreateTopics,
        selectedTopicId: state.mode === 'single-topic' ? state.selectedTopic : undefined,
      };

      const result = await parseQuestionCSVEnhanced(state.selectedFile, config);
      
      // Generate error report for preview if there are errors
      let errorReport: ImportErrorReport | null = null;
      if (result.globalErrors.length > 0 || 
          Array.from(result.topicResults.values()).some(tr => tr.errors.length > 0)) {
        errorReport = importErrorReportingService.generateErrorReport(result);
      }

      setState(prev => ({
        ...prev,
        previewData: result,
        showPreview: true,
        importing: false,
        progress: 100,
        errorReport,
        currentStep: 'preview',
      }));

      if (result.globalErrors.length > 0) {
        toast({
          title: "Preview completed with warnings",
          description: `Found ${result.globalErrors.length} issues. Please review before importing.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Preview successful",
          description: `Ready to import ${result.totalRows} questions across ${result.topicResults.size} topic(s).`,
        });
      }
    } catch (error) {
      console.error("Preview error:", error);
      toast({
        title: "Preview error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
      setState(prev => ({ ...prev, importing: false, progress: 0, currentStep: 'upload' }));
    }
  };

  const handleConfirmImport = async () => {
    if (!state.previewData) {
      toast({
        title: "No preview data",
        description: "Please preview the import first.",
        variant: "destructive",
      });
      return;
    }

    // Validate import result before processing
    const validationErrors = batchImportService.validateImportResult(state.previewData);
    if (validationErrors.length > 0) {
      toast({
        title: "Validation failed",
        description: `Found ${validationErrors.length} validation errors. Please check your data.`,
        variant: "destructive",
      });
      return;
    }

    setState(prev => ({ 
      ...prev, 
      importing: true, 
      progress: 0,
      progressMessage: "Starting import...",
      batchResult: null,
      currentStep: 'importing',
    }));

    try {
      // Configure batch import with progress tracking
      const batchConfig: BatchImportConfig = {
        mode: state.mode,
        autoCreateTopics: state.autoCreateTopics,
        selectedTopicId: state.mode === 'single-topic' ? state.selectedTopic : undefined,
        batchSize: 10, // Process 10 questions per batch
        maxRetries: 3, // Retry failed operations up to 3 times
        progressCallback: (progress: BatchImportProgress) => {
          setState(prev => ({
            ...prev,
            progress: progress.percentage,
            progressMessage: progress.message,
          }));
        },
      };

      // Execute the batch import
      const batchResult = await batchImportService.executeBatchImport(
        state.previewData,
        batchConfig
      );

      // Generate comprehensive error report
      const errorReport = importErrorReportingService.generateErrorReport(
        state.previewData,
        batchResult
      );

      setState(prev => ({ 
        ...prev, 
        batchResult,
        errorReport,
        progress: 100,
        progressMessage: "Import completed",
        currentStep: 'results',
      }));

      // Generate success/error messages
      const hasErrors = batchResult.errors.length > 0;
      const partialSuccess = batchResult.totalQuestionsImported > 0 && hasErrors;

      let title: string;
      let description: string;
      let variant: "default" | "destructive" = "default";

      if (batchResult.success && !hasErrors) {
        title = "Import completed successfully";
        description = batchResult.topicsCreated.length > 0
          ? `Imported ${batchResult.totalQuestionsImported} questions across ${batchResult.totalTopicsProcessed} topic(s). Created ${batchResult.topicsCreated.length} new topic(s).`
          : `Imported ${batchResult.totalQuestionsImported} questions across ${batchResult.totalTopicsProcessed} topic(s).`;
      } else if (partialSuccess) {
        title = "Import completed with errors";
        description = `Imported ${batchResult.totalQuestionsImported} questions, but encountered ${batchResult.errors.length} errors. Check the results below for details.`;
        variant = "destructive";
      } else {
        title = "Import failed";
        description = `Failed to import questions. Encountered ${batchResult.errors.length} errors.`;
        variant = "destructive";
      }

      toast({
        title,
        description,
        variant,
      });
    } catch (error) {
      console.error("Batch import error:", error);
      toast({
        title: "Import error",
        description: error instanceof Error ? error.message : "An unknown error occurred during import",
        variant: "destructive",
      });
      setState(prev => ({ 
        ...prev, 
        progress: 0,
        progressMessage: "Import failed",
        currentStep: 'preview',
      }));
    } finally {
      setState(prev => ({ ...prev, importing: false }));
    }
  };

  const handleStartOver = () => {
    setState({
      mode: 'single-topic',
      selectedFile: null,
      selectedTopic: "",
      autoCreateTopics: false,
      previewData: null,
      importing: false,
      showPreview: false,
      progress: 0,
      progressMessage: "",
      batchResult: null,
      showHelp: false,
      errorReport: null,
      currentStep: 'upload',
    });
  };

  const renderProgressIndicator = () => {
    const steps = [
      { key: 'upload', label: 'Upload File', completed: state.currentStep !== 'upload' },
      { key: 'preview', label: 'Preview Data', completed: state.currentStep === 'importing' || state.currentStep === 'results' },
      { key: 'importing', label: 'Import Questions', completed: state.currentStep === 'results' },
      { key: 'results', label: 'View Results', completed: false },
    ];

    return (
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.key} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                step.completed 
                  ? 'bg-green-500 border-green-500 text-white' 
                  : state.currentStep === step.key
                    ? 'bg-blue-500 border-blue-500 text-white'
                    : 'bg-gray-200 border-gray-300 text-gray-500'
              }`}>
                {step.completed ? (
                  <CheckCircle2 className="h-4 w-4" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                step.completed || state.currentStep === step.key
                  ? 'text-gray-900'
                  : 'text-gray-500'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div className={`mx-4 h-0.5 w-16 ${
                  step.completed ? 'bg-green-500' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderModeSelector = () => (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <Label>Import Mode</Label>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent className="max-w-xs">
            <p>Choose how to import your questions:</p>
            <ul className="mt-1 text-xs space-y-1">
              <li>• <strong>Single Topic:</strong> All questions go to one selected topic</li>
              <li>• <strong>Multi Topic:</strong> Questions are distributed to topics specified in the CSV</li>
            </ul>
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="flex gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={state.mode === 'single-topic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('single-topic')}
              disabled={state.importing}
            >
              <FileText className="h-4 w-4 mr-2" />
              Single Topic
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Import all questions to one selected topic. Use standard CSV format without topic columns.
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant={state.mode === 'multi-topic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => handleModeChange('multi-topic')}
              disabled={state.importing}
            >
              <Plus className="h-4 w-4 mr-2" />
              Multi Topic
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Import questions to multiple topics. CSV must include topic_name or topic_id columns.
          </TooltipContent>
        </Tooltip>
      </div>
      <div className="p-3 bg-muted/50 rounded-lg">
        <p className="text-sm text-muted-foreground">
          {state.mode === 'single-topic' 
            ? "📄 Import all questions to a single selected topic. Your CSV should not include topic columns."
            : "📁 Import questions to multiple topics specified in the CSV file. Include topic_name or topic_id columns."
          }
        </p>
      </div>
    </div>
  );

  const renderTopicConfiguration = () => {
    if (state.mode === 'single-topic') {
      return (
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="topic">Select Topic</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                All questions from your CSV will be imported to this topic. Make sure you have permission to add questions to the selected topic.
              </TooltipContent>
            </Tooltip>
          </div>

          {topicsLoading ? (
            <div className="flex items-center gap-2 p-3 border rounded-md">
              <Spinner className="h-4 w-4" />
              <span className="text-sm text-muted-foreground">Loading topics...</span>
            </div>
          ) : topics.length === 0 ? (
            <div className="p-3 border rounded-md bg-muted/50">
              <p className="text-sm text-muted-foreground">
                No topics available. Please create topics first in the admin dashboard.
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigate('/admin')}
                className="mt-2"
              >
                Go to Admin Dashboard
              </Button>
            </div>
          ) : (
            <Select value={state.selectedTopic} onValueChange={handleTopicChange} disabled={state.importing}>
              <SelectTrigger id="topic">
                <SelectValue placeholder="Select a topic" />
              </SelectTrigger>
              <SelectContent>
                {topics.map((topic) => (
                  <SelectItem key={topic.id} value={topic.id}>
                    <div className="flex flex-col items-start w-full">
                      <div className="flex items-center justify-between w-full">
                        <span className="font-medium">{topic.title}</span>
                        <div className="flex gap-1">
                          <Badge variant="outline" className="text-xs">
                            {topic.is_active ? 'Active' : 'Inactive'}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {topic.difficulty}
                          </Badge>
                        </div>
                      </div>
                      {topic.domain && (
                        <span className="text-xs text-muted-foreground">
                          Domain: {topic.domain.name}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <p className="text-xs text-muted-foreground">
            💡 All questions in your CSV will be added to the selected topic
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="auto-create">Auto-create Topics</Label>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>When enabled:</p>
                <ul className="mt-1 text-xs space-y-1">
                  <li>• New topics will be created automatically if they don't exist</li>
                  <li>• Topic names must be 1-100 characters</li>
                  <li>• Only letters, numbers, spaces, hyphens, and underscores allowed</li>
                </ul>
                <p className="mt-2 text-xs">When disabled, only existing topics will be used.</p>
              </TooltipContent>
            </Tooltip>
          </div>
          <Switch
            id="auto-create"
            checked={state.autoCreateTopics}
            onCheckedChange={handleAutoCreateToggle}
            disabled={state.importing}
          />
        </div>
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground">
            {state.autoCreateTopics
              ? "✅ New topics will be created automatically if they don't exist in the system"
              : "⚠️ Only questions for existing topics will be imported. Missing topics will cause errors"
            }
          </p>
        </div>
      </div>
    );
  };

  const renderFileUpload = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload CSV File
        </CardTitle>
        <CardDescription>
          Select a CSV file containing quiz questions to import
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="file">CSV File</Label>
          <Input
            id="file"
            type="file"
            accept=".csv"
            onChange={handleFileChange}
            disabled={state.importing}
          />
          {state.selectedFile && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <FileText className="h-4 w-4" />
              <span>{state.selectedFile.name}</span>
              <Badge variant="outline">
                {(state.selectedFile.size / 1024).toFixed(1)} KB
              </Badge>
            </div>
          )}
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Download Templates</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setState(prev => ({ ...prev, showHelp: !prev.showHelp }))}
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Help
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDownloadTemplate('single')}
            >
              <Download className="h-4 w-4 mr-2" />
              Single Topic Template
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Multi Topic Templates
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'name-based')}>
                  Name-based Template
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'id-based')}>
                  ID-based Template
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDownloadTemplate('multi', 'both')}>
                  Both Columns Template
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {state.showHelp && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>CSV Format Requirements</AlertTitle>
            <AlertDescription>
              <ul className="mt-2 text-sm space-y-1">
                <li>• Use UTF-8 encoding</li>
                <li>• Question text: minimum 10 characters</li>
                <li>• Explanation: minimum 10 characters</li>
                <li>• Correct answer: A, B, C, or D</li>
                <li>• Difficulty: easy, medium, or hard</li>
                <li>• For multi-topic: include topic_name or topic_id column</li>
              </ul>
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );

  const renderPreview = () => {
    if (!state.showPreview || !state.previewData) return null;

    const { previewData } = state;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Import Preview
          </CardTitle>
          <CardDescription>
            Review your data before importing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Global errors */}
          {previewData.globalErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Issues Found ({previewData.globalErrors.length})</AlertTitle>
              <AlertDescription>
                <p className="mb-2 text-sm">Please fix these issues before importing:</p>
                <div className="mt-2 max-h-32 overflow-y-auto">
                  <ul className="list-disc pl-5 text-sm">
                    {previewData.globalErrors.map((error, index) => (
                      <li key={index}>
                        <strong>Row {error.row}:</strong> {error.message}
                      </li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* New topics that will be created */}
          {previewData.newTopicsCreated.length > 0 && (
            <Alert className="border-blue-200 bg-blue-50">
              <Info className="h-4 w-4 text-blue-600" />
              <AlertTitle className="text-blue-800">New Topics Will Be Created ({previewData.newTopicsCreated.length})</AlertTitle>
              <AlertDescription className="text-blue-700">
                <p className="mb-2">The following topics don't exist and will be created automatically:</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {previewData.newTopicsCreated.map((topicName) => (
                    <Badge key={topicName} variant="secondary" className="bg-blue-100 text-blue-800">
                      {topicName}
                    </Badge>
                  ))}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Topic results */}
          <div className="space-y-3">
            {Array.from(previewData.topicResults.entries()).map(([topicId, topicResult]) => (
              <Card key={topicId} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium">{topicResult.topicName}</h4>
                    {topicResult.isNewTopic && (
                      <Badge variant="outline" className="text-xs">New</Badge>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="default">
                      {topicResult.validQuestions.length} questions
                    </Badge>
                    {topicResult.errors.length > 0 && (
                      <Badge variant="destructive">
                        {topicResult.errors.length} errors
                      </Badge>
                    )}
                  </div>
                </div>
                
                {topicResult.errors.length > 0 && (
                  <div className="mt-2 p-3 bg-destructive/10 rounded text-sm border border-destructive/20">
                    <p className="font-medium text-destructive mb-2">❌ Errors in this topic ({topicResult.errors.length}):</p>
                    <ul className="list-disc pl-5 text-destructive max-h-24 overflow-y-auto space-y-1">
                      {topicResult.errors.map((error, index) => (
                        <li key={index}>
                          <strong>Row {error.row}:</strong> {error.message}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </Card>
            ))}
          </div>

          {/* Summary */}
          <div className="p-4 bg-muted/50 rounded-lg border">
            <h4 className="font-medium mb-2">📊 Import Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground">Total Rows</p>
                <p className="font-medium">{previewData.totalRows}</p>
              </div>
              <div>
                <p className="text-muted-foreground">Valid Questions</p>
                <p className="font-medium text-green-600">
                  {Array.from(previewData.topicResults.values()).reduce((sum, tr) => sum + tr.validQuestions.length, 0)}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Topics</p>
                <p className="font-medium">{previewData.topicResults.size}</p>
              </div>
              {previewData.newTopicsCreated.length > 0 && (
                <div>
                  <p className="text-muted-foreground">New Topics</p>
                  <p className="font-medium text-blue-600">{previewData.newTopicsCreated.length}</p>
                </div>
              )}
            </div>
            {previewData.globalErrors.length === 0 && (
              <div className="mt-3 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
                ✅ Ready to import! All questions passed validation.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderImportProgress = () => {
    if (state.currentStep !== 'importing') return null;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Spinner className="h-5 w-5" />
            Importing Questions
          </CardTitle>
          <CardDescription>
            Please wait while we import your questions...
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{state.progressMessage}</span>
              <span>{state.progress}%</span>
            </div>
            <Progress value={state.progress} className="w-full" />
          </div>
          
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Large imports may take several minutes. Please don't close this page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  };

  const renderBatchResults = () => {
    if (!state.batchResult || state.currentStep !== 'results') return null;

    const { batchResult } = state;

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {batchResult.success ? (
              <CheckCircle2 className="h-5 w-5 text-green-600" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            Import Results
          </CardTitle>
          <CardDescription>
            {batchResult.success ? 'Import completed successfully' : 'Import completed with issues'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall result summary */}
          <Alert className={`${batchResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            {batchResult.success ? (
              <CheckCircle2 className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertTitle className={batchResult.success ? 'text-green-800' : 'text-red-800'}>
              {batchResult.success ? 'Import Completed' : 'Import Failed'}
            </AlertTitle>
            <AlertDescription className={batchResult.success ? 'text-green-700' : 'text-red-700'}>
              <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <p className="font-medium">Questions imported:</p>
                  <p className="text-lg">{batchResult.totalQuestionsImported}</p>
                </div>
                <div>
                  <p className="font-medium">Topics processed:</p>
                  <p className="text-lg">{batchResult.totalTopicsProcessed}</p>
                </div>
                {batchResult.topicsCreated.length > 0 && (
                  <div>
                    <p className="font-medium">New topics created:</p>
                    <p className="text-lg">{batchResult.topicsCreated.length}</p>
                  </div>
                )}
                <div>
                  <p className="font-medium">Duration:</p>
                  <p className="text-lg">{Math.round(batchResult.duration / 1000)}s</p>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Batch errors */}
          {batchResult.errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Import Errors ({batchResult.errors.length})</AlertTitle>
              <AlertDescription>
                <div className="mt-2 max-h-32 overflow-y-auto">
                  <ul className="list-disc pl-5 text-sm">
                    {batchResult.errors.map((error, index) => (
                      <li key={index}>
                        <strong>{error.type}:</strong> {error.message}
                        {error.topicName && ` (Topic: ${error.topicName})`}
                      </li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Topic-level results */}
          <div className="space-y-3">
            <h4 className="font-medium">Topic Results</h4>
            {Array.from(batchResult.topicResults.entries()).map(([topicId, topicResult]) => (
              <Card key={topicId} className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h5 className="font-medium">{topicResult.topicName}</h5>
                    {topicResult.isNewTopic && (
                      <Badge variant="outline" className="text-xs">New</Badge>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Badge variant="default">
                      {topicResult.questionsImported} imported
                    </Badge>
                    {topicResult.questionsFailed > 0 && (
                      <Badge variant="destructive">
                        {topicResult.questionsFailed} failed
                      </Badge>
                    )}
                  </div>
                </div>
                
                <div className="text-sm text-muted-foreground">
                  Duration: {Math.round(topicResult.duration / 1000)}s
                </div>
                
                {topicResult.errors.length > 0 && (
                  <div className="mt-2 p-2 bg-destructive/10 rounded text-sm">
                    <p className="font-medium text-destructive mb-1">Topic Errors:</p>
                    <ul className="list-disc pl-5 text-destructive max-h-24 overflow-y-auto">
                      {topicResult.errors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </Card>
            ))}
          </div>

          {/* Error reporting component */}
          {state.errorReport && (
            <div className="space-y-3">
              <h4 className="font-medium">Detailed Error Report</h4>
              <ImportErrorReporting errorReport={state.errorReport} />
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  if (topicsLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <Spinner className="h-8 w-8" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/admin')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Admin
            </Button>
          </div>
          <h1 className="text-3xl font-bold">Import Questions</h1>
          <p className="text-muted-foreground mt-2">
            Import quiz questions from CSV files with comprehensive error reporting and batch processing
          </p>
        </div>

        {/* Progress Indicator */}
        {renderProgressIndicator()}

        {/* Main Content */}
        <div className="space-y-6">
          {/* Step 1: Upload and Configuration */}
          {state.currentStep === 'upload' && (
            <>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-6">
                  {renderModeSelector()}
                  {renderTopicConfiguration()}
                </div>
                <div>
                  {renderFileUpload()}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2">
                <Button
                  onClick={handlePreview}
                  disabled={!state.selectedFile || state.importing || (state.mode === 'single-topic' && !state.selectedTopic)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {state.importing ? (
                    <>
                      <Spinner className="h-4 w-4 mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <FileText className="h-4 w-4 mr-2" />
                      Preview Import
                    </>
                  )}
                </Button>
              </div>
            </>
          )}

          {/* Step 2: Preview */}
          {state.currentStep === 'preview' && (
            <>
              {renderPreview()}
              
              {/* Action Buttons */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => setState(prev => ({ ...prev, currentStep: 'upload' }))}
                  disabled={state.importing}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Upload
                </Button>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleStartOver}
                    disabled={state.importing}
                  >
                    Start Over
                  </Button>
                  <Button
                    onClick={handleConfirmImport}
                    disabled={state.importing || (state.previewData?.globalErrors.length ?? 0) > 0}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Confirm Import
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* Step 3: Importing */}
          {state.currentStep === 'importing' && renderImportProgress()}

          {/* Step 4: Results */}
          {state.currentStep === 'results' && (
            <>
              {renderBatchResults()}
              
              {/* Action Buttons */}
              <div className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => navigate('/admin')}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Admin
                </Button>
                
                <Button
                  onClick={handleStartOver}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Import More Questions
                </Button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}