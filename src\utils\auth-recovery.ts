/**
 * Authentication Recovery Utility
 * Handles expired sessions and provides recovery mechanisms
 */

import { supabase } from '@/integrations/supabase/client';
import { authRateLimiter, executeWithRateLimit, isRateLimitError } from './rate-limiter';

/**
 * Clear expired session and reset to anonymous state
 */
export async function clearExpiredSession(): Promise<void> {
  try {
    console.log('Clearing expired session...');
    
    // Sign out to clear any expired tokens
    await supabase.auth.signOut();
    
    // Clear any cached session data
    localStorage.removeItem('supabase.auth.token');
    sessionStorage.removeItem('supabase.auth.token');
    
    console.log('Session cleared successfully');
  } catch (error) {
    console.warn('Error clearing session:', error);
    // Continue anyway - the session might already be cleared
  }
}

/**
 * Check if the current session is expired and needs clearing
 */
export async function isSessionExpired(): Promise<boolean> {
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.warn('Session check error:', error);
      return true; // Assume expired if we can't check
    }
    
    if (!session) {
      return false; // No session is not expired
    }
    
    // Check if session is expired
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = session.expires_at || 0;
    
    return expiresAt <= now;
  } catch (error) {
    console.warn('Error checking session expiration:', error);
    return true; // Assume expired on error
  }
}

/**
 * Attempt to recover from authentication errors
 * Returns true if recovery was successful, false if user needs to sign in
 */
export async function attemptAuthRecovery(): Promise<boolean> {
  try {
    console.log('Attempting authentication recovery...');

    // Check rate limit before attempting recovery
    if (!authRateLimiter.isAllowed('token_refresh')) {
      const timeUntilReset = authRateLimiter.getTimeUntilReset('token_refresh');
      console.warn(`Auth recovery rate limited. Try again in ${Math.ceil(timeUntilReset / 1000)} seconds.`);
      return false;
    }

    try {
      // First, try to refresh the session with rate limiting
      const { data, error } = await executeWithRateLimit(
        'token_refresh',
        () => supabase.auth.refreshSession()
      );

      if (!error && data.session) {
        console.log('Session refreshed successfully during recovery');
        return true;
      }

      // If refresh failed but it's not a rate limit error, log it
      if (error && !isRateLimitError(error)) {
        console.log('Session refresh failed during recovery:', error.message);
      }

    } catch (rateLimitError: any) {
      console.warn('Auth recovery rate limited:', rateLimitError.message);
      return false;
    }

    console.log('Session refresh failed, but NOT clearing session to prevent admin logout');

    // DON'T automatically clear session - this was causing admin logouts
    // Let the user manually sign out if needed

    // Return false to indicate user needs to sign in again
    return false;

  } catch (error) {
    console.error('Error during auth recovery:', error);

    // DON'T automatically clear session on errors
    // This was causing the automatic sign-outs

    return false;
  }
}

/**
 * Handle authentication errors gracefully
 * Provides user-friendly error messages and recovery suggestions
 */
export function handleAuthError(error: any): {
  message: string;
  action: 'refresh' | 'signin' | 'retry';
  severity: 'warning' | 'error';
} {
  if (!error) {
    return {
      message: 'Unknown error occurred',
      action: 'retry',
      severity: 'error'
    };
  }
  
  // JWT expired errors
  if (error.code === 'PGRST301' || error.message?.includes('JWT expired')) {
    return {
      message: 'Your session has expired. Please refresh the page to continue.',
      action: 'refresh',
      severity: 'warning'
    };
  }
  
  // Permission denied errors
  if (error.code === 'PGRST401' || error.message?.includes('permission denied')) {
    return {
      message: 'Access denied. Please sign in to continue.',
      action: 'signin',
      severity: 'warning'
    };
  }
  
  // Session invalid errors
  if (error.code === 'SESSION_INVALID') {
    return {
      message: 'Please sign in to access this feature.',
      action: 'signin',
      severity: 'warning'
    };
  }
  
  // Network or other errors
  return {
    message: 'Unable to load data. Please check your connection and try again.',
    action: 'retry',
    severity: 'error'
  };
}

/**
 * Initialize authentication recovery on app start
 * Clears any expired sessions automatically
 */
export async function initializeAuthRecovery(): Promise<void> {
  try {
    const expired = await isSessionExpired();
    
    if (expired) {
      console.log('Detected expired session on app start, clearing...');
      await clearExpiredSession();
    }
  } catch (error) {
    console.warn('Error during auth recovery initialization:', error);
  }
}
