-- Quiz Answer Format Standardization Script
-- This script converts all numeric format questions (0,1,2,3) to letter format (A,B,C,D)
-- Run this in Supabase SQL editor to standardize answer formats

-- First, let's see what we're working with
SELECT 
  'Current State Analysis' as analysis_type,
  CASE 
    WHEN correct_answer ~ '^[0-9]+$' THEN 'numeric'
    WHEN correct_answer ~ '^[A-D]$' THEN 'letter'
    ELSE 'other'
  END as format_type,
  COUNT(*) as count
FROM public.questions 
WHERE correct_answer IS NOT NULL
GROUP BY format_type
ORDER BY format_type;

-- Step 1: Convert numeric options format to letter format
-- Update questions that have numeric keys (0,1,2,3) to letter keys (A,B,C,D)
UPDATE public.questions 
SET options = jsonb_build_object(
  'A', options->'0',
  'B', options->'1', 
  'C', options->'2',
  'D', options->'3'
)
WHERE options ? '0' 
  AND options ? '1' 
  AND options ? '2' 
  AND options ? '3'
  AND NOT (options ? 'A');

-- Step 2: Convert numeric correct_answer format to letter format
-- Map: 0->A, 1->B, 2->C, 3->D
UPDATE public.questions 
SET correct_answer = CASE 
  WHEN correct_answer = '0' THEN 'A'
  WHEN correct_answer = '1' THEN 'B'
  WHEN correct_answer = '2' THEN 'C'
  WHEN correct_answer = '3' THEN 'D'
  ELSE correct_answer
END
WHERE correct_answer IN ('0', '1', '2', '3');

-- Step 3: Verify the conversion
SELECT 
  'Post-Conversion Analysis' as analysis_type,
  CASE 
    WHEN correct_answer ~ '^[0-9]+$' THEN 'numeric'
    WHEN correct_answer ~ '^[A-D]$' THEN 'letter'
    ELSE 'other'
  END as format_type,
  COUNT(*) as count
FROM public.questions 
WHERE correct_answer IS NOT NULL
GROUP BY format_type
ORDER BY format_type;

-- Step 4: Verify options format
SELECT 
  'Options Format Analysis' as analysis_type,
  CASE 
    WHEN options ? 'A' THEN 'letter_keys'
    WHEN options ? '0' THEN 'numeric_keys'
    ELSE 'other'
  END as options_format,
  COUNT(*) as count
FROM public.questions 
WHERE options IS NOT NULL
GROUP BY options_format;

-- Step 5: Sample verification - show some converted questions
SELECT 
  id,
  question_text,
  options,
  correct_answer,
  'Converted Question Sample' as note
FROM public.questions 
WHERE correct_answer IN ('A', 'B', 'C', 'D')
  AND options ? 'A'
LIMIT 5;

-- Add a comment to track this migration
COMMENT ON TABLE public.questions IS 'Quiz questions table - Answer format standardized to letter format (A,B,C,D) on 2025-09-09';
