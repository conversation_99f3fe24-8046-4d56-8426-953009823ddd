/**
 * Enhanced Content Security Policy and security headers middleware
 */

import { Request, Response, NextFunction } from 'express';
import { serverConfig } from '../lib/config.js';
import { secureLogger } from '../lib/secure-logger.js';

export const cspMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const isProduction = serverConfig.nodeEnv === 'production';
  
  // Define CSP directives based on environment
  const cspDirectives = [
    "default-src 'self'",
    isProduction
      ? "script-src 'self'"
      : "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Allow inline scripts in dev only
    "connect-src 'self' https://*.supabase.co wss://*.supabase.co https://api.paystack.co https://checkout.paystack.com",
    isProduction
      ? "style-src 'self' https://fonts.googleapis.com"
      : "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com", // Allow Google Fonts and inline styles
    "style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com", // Explicit style-src-elem for Google Fonts
    "img-src 'self' data: https: blob:", // Allow blob URLs for image uploads
    "font-src 'self' data: https://fonts.gstatic.com", // Allow Google Fonts
    "frame-src 'self' https://checkout.paystack.com",
    "frame-ancestors 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self' https://checkout.paystack.com",
    "upgrade-insecure-requests"
  ].join('; ');

  // Set Content Security Policy
  res.setHeader('Content-Security-Policy', cspDirectives);
  
  // Set comprehensive security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // HSTS (HTTP Strict Transport Security) - only in production with HTTPS
  if (isProduction) {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  // Permissions Policy (formerly Feature Policy)
  res.setHeader('Permissions-Policy', [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=(self "https://checkout.paystack.com")',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()'
  ].join(', '));
  
  // Additional security headers
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
  res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
  
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  next();
};

export default cspMiddleware;
