/**
 * Test script for the rate limiter functionality
 * Run with: node scripts/test-rate-limiter.js
 */

// Mock the rate limiter for testing
class TestAuthRateLimiter {
  constructor() {
    this.limits = new Map();
    this.configs = new Map();

    // Configure rate limits for different auth operations
    this.configs.set('token_refresh', {
      maxRequests: 5,        // Max 5 refresh attempts
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 5000       // 5 second cooldown between attempts
    });

    this.configs.set('auth_request', {
      maxRequests: 10,       // Max 10 auth requests
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 1000       // 1 second cooldown between attempts
    });

    this.configs.set('session_check', {
      maxRequests: 20,       // Max 20 session checks
      windowMs: 60000,       // Per 1 minute
      cooldownMs: 500        // 500ms cooldown between attempts
    });
  }

  isAllowed(operation, identifier = 'global') {
    const config = this.configs.get(operation);
    if (!config) {
      console.warn(`No rate limit config found for operation: ${operation}`);
      return true;
    }

    const key = `${operation}:${identifier}`;
    const now = Date.now();
    const entry = this.limits.get(key);

    // If no entry exists, create one and allow the request
    if (!entry) {
      this.limits.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
        lastRequest: now
      });
      return true;
    }

    // Check if the window has expired
    if (now >= entry.resetTime) {
      this.limits.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
        lastRequest: now
      });
      return true;
    }

    // Check cooldown period
    if (now - entry.lastRequest < config.cooldownMs) {
      console.warn(`Rate limit cooldown active for ${operation}. Please wait ${config.cooldownMs - (now - entry.lastRequest)}ms`);
      return false;
    }

    // Check if we've exceeded the limit
    if (entry.count >= config.maxRequests) {
      const timeUntilReset = entry.resetTime - now;
      console.warn(`Rate limit exceeded for ${operation}. Reset in ${timeUntilReset}ms`);
      return false;
    }

    // Update the entry and allow the request
    entry.count++;
    entry.lastRequest = now;
    return true;
  }

  getTimeUntilReset(operation, identifier = 'global') {
    const key = `${operation}:${identifier}`;
    const entry = this.limits.get(key);
    
    if (!entry) {
      return 0;
    }

    const now = Date.now();
    return Math.max(0, entry.resetTime - now);
  }

  getRemainingRequests(operation, identifier = 'global') {
    const config = this.configs.get(operation);
    if (!config) {
      return Infinity;
    }

    const key = `${operation}:${identifier}`;
    const entry = this.limits.get(key);
    
    if (!entry) {
      return config.maxRequests;
    }

    const now = Date.now();
    if (now >= entry.resetTime) {
      return config.maxRequests;
    }

    return Math.max(0, config.maxRequests - entry.count);
  }

  reset(operation, identifier = 'global') {
    const key = `${operation}:${identifier}`;
    this.limits.delete(key);
  }

  getStatus() {
    const status = {};
    
    for (const [operation, config] of this.configs.entries()) {
      status[operation] = {
        config,
        remaining: this.getRemainingRequests(operation),
        timeUntilReset: this.getTimeUntilReset(operation)
      };
    }

    return status;
  }
}

// Test the rate limiter
async function testRateLimiter() {
  console.log('🧪 Testing Rate Limiter Functionality\n');

  const rateLimiter = new TestAuthRateLimiter();

  // Test 1: Normal operation within limits
  console.log('📋 Test 1: Normal operation within limits');
  for (let i = 1; i <= 3; i++) {
    const allowed = rateLimiter.isAllowed('token_refresh');
    console.log(`  Request ${i}: ${allowed ? '✅ Allowed' : '❌ Denied'}`);
    
    if (allowed) {
      const remaining = rateLimiter.getRemainingRequests('token_refresh');
      console.log(`    Remaining requests: ${remaining}`);
    }
  }

  // Test 2: Exceeding rate limits
  console.log('\n📋 Test 2: Exceeding rate limits');
  for (let i = 4; i <= 7; i++) {
    const allowed = rateLimiter.isAllowed('token_refresh');
    console.log(`  Request ${i}: ${allowed ? '✅ Allowed' : '❌ Denied'}`);
    
    if (!allowed) {
      const timeUntilReset = rateLimiter.getTimeUntilReset('token_refresh');
      console.log(`    Time until reset: ${Math.ceil(timeUntilReset / 1000)}s`);
    }
  }

  // Test 3: Cooldown period
  console.log('\n📋 Test 3: Cooldown period testing');
  rateLimiter.reset('session_check'); // Reset for clean test
  
  console.log('  Making rapid session check requests...');
  for (let i = 1; i <= 5; i++) {
    const allowed = rateLimiter.isAllowed('session_check');
    console.log(`    Request ${i}: ${allowed ? '✅ Allowed' : '❌ Denied (cooldown)'}`);
    
    // Small delay to test cooldown
    if (i < 5) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  // Test 4: Status reporting
  console.log('\n📋 Test 4: Status reporting');
  const status = rateLimiter.getStatus();
  console.log('  Rate limiter status:');
  for (const [operation, info] of Object.entries(status)) {
    console.log(`    ${operation}:`);
    console.log(`      Max requests: ${info.config.maxRequests}`);
    console.log(`      Window: ${info.config.windowMs}ms`);
    console.log(`      Cooldown: ${info.config.cooldownMs}ms`);
    console.log(`      Remaining: ${info.remaining}`);
    console.log(`      Reset in: ${Math.ceil(info.timeUntilReset / 1000)}s`);
  }

  console.log('\n✅ Rate limiter tests completed successfully!');
  console.log('\n📝 Summary:');
  console.log('  - Rate limiting prevents excessive requests');
  console.log('  - Cooldown periods prevent rapid-fire requests');
  console.log('  - Status reporting provides debugging information');
  console.log('  - Different operations have appropriate limits');
}

// Run the tests
testRateLimiter().catch(console.error);
