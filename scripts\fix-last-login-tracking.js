/**
 * <PERSON><PERSON><PERSON> to diagnose and fix last login tracking issues
 * This script will check the database schema and fix any issues with last_login_at tracking
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Check if last_login_at column exists in user_profiles table
 */
async function checkLastLoginColumn() {
  console.log('🔍 Checking user_profiles table structure...\n');

  try {
    // Get a sample of user profiles to check the schema
    const { data: profiles, error } = await supabase
      .from('user_profiles')
      .select('*')
      .limit(1);

    if (error) {
      console.error('❌ Error querying user_profiles:', error.message);
      return false;
    }

    if (profiles && profiles.length > 0) {
      const profile = profiles[0];
      console.log('📋 Current user_profiles schema:');
      console.log('   Columns:', Object.keys(profile).join(', '));
      
      const hasLastLoginAt = 'last_login_at' in profile;
      console.log(`   Has last_login_at column: ${hasLastLoginAt ? '✅ YES' : '❌ NO'}\n`);
      
      return hasLastLoginAt;
    } else {
      console.log('⚠️  No user profiles found in database\n');
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking schema:', error);
    return false;
  }
}

/**
 * Check current last_login_at values
 */
async function checkLastLoginValues() {
  console.log('🔍 Checking current last_login_at values...\n');

  try {
    const { data: profiles, error } = await supabase
      .from('user_profiles')
      .select('id, user_id, email, last_login_at, created_at, updated_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) {
      console.error('❌ Error querying user profiles:', error.message);
      return;
    }

    console.log(`📊 Found ${profiles?.length || 0} user profiles\n`);

    if (profiles && profiles.length > 0) {
      console.log('📋 Sample user profiles:');
      profiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. Email: ${profile.email || 'N/A'}`);
        console.log(`      User ID: ${profile.user_id}`);
        console.log(`      Last Login: ${profile.last_login_at || 'NULL'}`);
        console.log(`      Created: ${profile.created_at}`);
        console.log(`      Updated: ${profile.updated_at}\n`);
      });

      // Count profiles with null last_login_at
      const nullLastLogin = profiles.filter(p => !p.last_login_at).length;
      const hasLastLogin = profiles.filter(p => p.last_login_at).length;
      
      console.log(`📈 Statistics:`);
      console.log(`   Profiles with last_login_at: ${hasLastLogin}`);
      console.log(`   Profiles with NULL last_login_at: ${nullLastLogin}`);
      console.log(`   Percentage with NULL: ${((nullLastLogin / profiles.length) * 100).toFixed(1)}%\n`);
    }
  } catch (error) {
    console.error('❌ Error checking last login values:', error);
  }
}

/**
 * Check auth.users table for last_sign_in_at values
 */
async function checkAuthUsersLastSignIn() {
  console.log('🔍 Checking auth.users last_sign_in_at values...\n');

  try {
    // Note: We can't directly query auth.users from client-side, but we can check if the sync is working
    // by looking at recent user_profiles updates
    
    const { data: recentProfiles, error } = await supabase
      .from('user_profiles')
      .select('user_id, email, last_login_at, updated_at')
      .not('last_login_at', 'is', null)
      .order('last_login_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ Error querying recent logins:', error.message);
      return;
    }

    console.log(`📊 Recent logins (${recentProfiles?.length || 0} found):`);
    if (recentProfiles && recentProfiles.length > 0) {
      recentProfiles.forEach((profile, index) => {
        console.log(`   ${index + 1}. ${profile.email || 'N/A'} - ${profile.last_login_at}`);
      });
    } else {
      console.log('   ⚠️  No recent logins found with last_login_at values');
    }
    console.log('');
  } catch (error) {
    console.error('❌ Error checking recent logins:', error);
  }
}

/**
 * Test the login tracking by simulating a login update
 */
async function testLoginTracking() {
  console.log('🧪 Testing login tracking functionality...\n');

  try {
    // Get current user (if any)
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      console.log('⚠️  No authenticated user found. Cannot test login tracking.');
      console.log('   To test login tracking, you need to be logged in.\n');
      return;
    }

    console.log(`👤 Testing with user: ${user.email}`);
    
    // Try to update the last_login_at for current user
    const { error: updateError } = await supabase
      .from('user_profiles')
      .upsert({
        user_id: user.id,
        email: user.email,
        last_login_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (updateError) {
      console.error('❌ Error updating last_login_at:', updateError.message);
      return;
    }

    console.log('✅ Successfully updated last_login_at for current user');
    
    // Verify the update
    const { data: updatedProfile, error: fetchError } = await supabase
      .from('user_profiles')
      .select('last_login_at, updated_at')
      .eq('user_id', user.id)
      .single();

    if (fetchError) {
      console.error('❌ Error fetching updated profile:', fetchError.message);
      return;
    }

    console.log(`✅ Verified update:`);
    console.log(`   Last Login: ${updatedProfile.last_login_at}`);
    console.log(`   Updated At: ${updatedProfile.updated_at}\n`);

  } catch (error) {
    console.error('❌ Error testing login tracking:', error);
  }
}

/**
 * Provide recommendations for fixing the issue
 */
function provideRecommendations(hasLastLoginColumn) {
  console.log('💡 RECOMMENDATIONS:\n');

  if (!hasLastLoginColumn) {
    console.log('❌ CRITICAL: last_login_at column is missing from user_profiles table');
    console.log('   📝 Action needed: Run the database migration to add the column');
    console.log('   📄 File: supabase/migrations/20240801000001_add_last_login_at_column.sql');
    console.log('   🔧 Or run this SQL in Supabase dashboard:');
    console.log('      ALTER TABLE public.user_profiles ADD COLUMN last_login_at TIMESTAMPTZ;\n');
  } else {
    console.log('✅ last_login_at column exists in user_profiles table');
  }

  console.log('🔧 Additional checks to perform:');
  console.log('   1. Verify the auth trigger is working:');
  console.log('      - Check if sync_auth_user_last_login trigger exists on auth.users');
  console.log('      - Verify the sync_user_last_login() function exists');
  console.log('   2. Test the auth service:');
  console.log('      - Check if signIn() function in auth-service.ts is being called');
  console.log('      - Verify the upsert operation in signIn() is working');
  console.log('   3. Check RLS policies:');
  console.log('      - Ensure user_profiles table has proper RLS policies');
  console.log('      - Verify users can update their own profiles\n');

  console.log('🚀 Quick fixes to try:');
  console.log('   1. Re-run the last_login_at migration');
  console.log('   2. Test login with a user account');
  console.log('   3. Check browser console for auth errors');
  console.log('   4. Verify Supabase project settings\n');
}

/**
 * Main diagnostic function
 */
async function main() {
  console.log('🔧 SecQuiz Last Login Tracking Diagnostic\n');
  console.log('This script will help diagnose why last login times show as "never"\n');

  // Step 1: Check if last_login_at column exists
  const hasLastLoginColumn = await checkLastLoginColumn();

  // Step 2: Check current values
  await checkLastLoginValues();

  // Step 3: Check recent logins
  await checkAuthUsersLastSignIn();

  // Step 4: Test login tracking (if user is authenticated)
  await testLoginTracking();

  // Step 5: Provide recommendations
  provideRecommendations(hasLastLoginColumn);

  console.log('🎯 SUMMARY:');
  console.log('   The most common cause of "never" last login times is:');
  console.log('   1. Missing last_login_at column in user_profiles table');
  console.log('   2. Auth service not properly updating the column on login');
  console.log('   3. Database trigger not syncing from auth.users table');
  console.log('   4. RLS policies preventing updates\n');

  console.log('✅ Diagnostic complete!');
}

// Run the diagnostic
main().catch(console.error);
