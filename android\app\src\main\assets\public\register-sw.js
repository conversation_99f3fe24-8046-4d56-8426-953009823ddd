// Register Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', async () => {
    try {
      // Check if service worker is already registered
      const existingRegistration = await navigator.serviceWorker.getRegistration('/');

      if (existingRegistration && existingRegistration.active) {
        // Service worker already registered and active - just log once
        if (!window.swLoggedOnce) {
          console.log('ServiceWorker already active with scope:', existingRegistration.scope);
          window.swLoggedOnce = true;
        }
        return;
      }

      // Use root scope to avoid conflicts
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      // Log registration success
      console.log('ServiceWorker registration successful with scope:', registration.scope);

      // Handle service worker updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;

        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // New content is available, show update notification if desired
              console.log('New service worker installed - new content is available');
            }
          });
        }
      });
    } catch (error) {
      // Log service worker registration errors
      console.error('ServiceWorker registration failed:', error);
    }
  });

  // Handle service worker communication if needed
  navigator.serviceWorker.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'CACHE_UPDATED') {
      // Handle cache updates if needed
    }
  });
}
