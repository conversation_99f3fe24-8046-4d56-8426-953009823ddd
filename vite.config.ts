import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current working directory.
  const env = loadEnv(mode, process.cwd(), '');

  return {
    define: {
      // Explicitly define environment variables for the browser
      'import.meta.env.VITE_SUPABASE_URL': JSON.stringify(env.VITE_SUPABASE_URL),
      'import.meta.env.VITE_SUPABASE_ANON_KEY': JSON.stringify(env.VITE_SUPABASE_ANON_KEY),
    },
    server: {
      host: "localhost",
      port: 5173,
      strictPort: true, // Fail if port is already in use
      hmr: {
        port: 5173,
        host: "localhost"
      },
      fs: {
        allow: ['..']
      }
    },
    optimizeDeps: {
      // Include Supabase dependencies for proper bundling
      include: [
        '@supabase/supabase-js',
        '@supabase/postgrest-js'
      ],
      // Exclude problematic directories from scanning
      exclude: [
        'android/*',
        'server/*',
        'supabase/*',
        'scripts/*'
      ]
    },
    // Exclude problematic HTML files from entry points
    build: {
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'index.html')
        }
      }
    },
    plugins: [
      react(),
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
  };
});
