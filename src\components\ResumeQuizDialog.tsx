import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Clock, BookOpen, Play, Trash2, AlertTriangle } from "lucide-react";
import { useQuizSessionPersistence } from "@/hooks/use-quiz-session-persistence";
import { formatDistanceToNow } from "date-fns";

interface ResumeQuizDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onResume: () => void;
  onStartNew: () => void;
  topicTitle?: string;
}

const ResumeQuizDialog = ({ 
  open, 
  onOpenChange, 
  onResume, 
  onStartNew,
  topicTitle 
}: ResumeQuizDialogProps) => {
  const { activeSession, getSessionProgress, abandonSession } = useQuizSessionPersistence();

  if (!activeSession) return null;

  const progress = getSessionProgress();
  const timeElapsed = progress ? formatDistanceToNow(new Date(Date.now() - progress.timeElapsed), { addSuffix: false }) : '';

  const handleAbandonSession = () => {
    abandonSession();
    onStartNew();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Resume Previous Quiz?
          </DialogTitle>
          <DialogDescription>
            You have an active quiz session for this topic.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Session Info Card */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">
                {topicTitle || 'Quiz Session'}
              </CardTitle>
              <CardDescription className="text-xs">
                Started {timeElapsed} ago
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Progress */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">
                    {progress?.currentQuestion}/{progress?.totalQuestions} questions
                  </span>
                </div>
                <Progress 
                  value={progress?.progressPercentage || 0} 
                  className="h-2"
                />
                <div className="text-xs text-muted-foreground">
                  {progress?.answeredQuestions} of {progress?.totalQuestions} answered
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center gap-2 text-sm">
                  <BookOpen className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="font-medium">{progress?.totalQuestions}</div>
                    <div className="text-xs text-muted-foreground">Questions</div>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-green-500" />
                  <div>
                    <div className="font-medium">{timeElapsed}</div>
                    <div className="text-xs text-muted-foreground">Time spent</div>
                  </div>
                </div>
              </div>

              {/* Current Question Badge */}
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  Next: Question {progress?.currentQuestion}
                </Badge>
                <Badge 
                  variant={progress?.progressPercentage && progress.progressPercentage > 50 ? "default" : "outline"}
                  className="text-xs"
                >
                  {progress?.progressPercentage}% Complete
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button 
              onClick={() => {
                onResume();
                onOpenChange(false);
              }}
              className="w-full"
              size="lg"
            >
              <Play className="h-4 w-4 mr-2" />
              Resume Quiz
            </Button>
            
            <div className="grid grid-cols-2 gap-2">
              <Button 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                size="sm"
              >
                Cancel
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleAbandonSession}
                size="sm"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Start New
              </Button>
            </div>
          </div>

          {/* Warning */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-amber-800">
                <p className="font-medium mb-1">Important:</p>
                <p>
                  Starting a new quiz will permanently delete your current progress. 
                  This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ResumeQuizDialog;
