/**
 * Bulk Quiz Correction Script
 * Fixes all existing quizzes with incorrect answer mappings
 * Run with: node scripts/bulk-quiz-correction.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client with service key for admin operations
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - VITE_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Converts numeric options format to letter format
 * @param {Object} options - The options object
 * @returns {Object} - Converted options object
 */
function convertOptionsToLetterFormat(options) {
  if (!options || typeof options !== 'object') {
    return {
      A: 'Option A',
      B: 'Option B', 
      C: 'Option C',
      D: 'Option D'
    };
  }

  // If already in letter format, return as-is
  if (options.A && options.B && options.C && options.D) {
    return options;
  }

  // Convert from numeric format
  return {
    A: options['0'] || options[0] || 'Option A',
    B: options['1'] || options[1] || 'Option B',
    C: options['2'] || options[2] || 'Option C',
    D: options['3'] || options[3] || 'Option D'
  };
}

/**
 * Converts numeric correct answer to letter format
 * @param {string|number} correctAnswer - The correct answer
 * @returns {string} - Letter format answer (A, B, C, or D)
 */
function convertAnswerToLetterFormat(correctAnswer) {
  if (!correctAnswer && correctAnswer !== 0) {
    return 'A'; // Default fallback
  }

  const answer = correctAnswer.toString().trim().toUpperCase();

  // If already in letter format, return as-is
  if (['A', 'B', 'C', 'D'].includes(answer)) {
    return answer;
  }

  // Convert from numeric format
  switch (answer) {
    case '0': return 'A';
    case '1': return 'B';
    case '2': return 'C';
    case '3': return 'D';
    default: return 'A'; // Default fallback
  }
}

/**
 * Analyzes the current state of questions in the database
 */
async function analyzeCurrentState() {
  console.log('🔍 Analyzing current quiz question formats...\n');

  try {
    // Get total question count
    const { count: totalQuestions, error: countError } = await supabase
      .from('questions')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(`Failed to count questions: ${countError.message}`);
    }

    console.log(`📊 Total questions in database: ${totalQuestions}`);

    // Analyze answer formats
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('id, correct_answer, options');

    if (questionsError) {
      throw new Error(`Failed to fetch questions: ${questionsError.message}`);
    }

    let letterFormat = 0;
    let numericFormat = 0;
    let otherFormat = 0;
    let letterOptions = 0;
    let numericOptions = 0;
    let problematicQuestions = [];

    questions.forEach(question => {
      const answer = question.correct_answer?.toString().trim().toUpperCase();
      const options = question.options;

      // Analyze answer format
      if (['A', 'B', 'C', 'D'].includes(answer)) {
        letterFormat++;
      } else if (['0', '1', '2', '3'].includes(answer)) {
        numericFormat++;
      } else {
        otherFormat++;
        problematicQuestions.push({
          id: question.id,
          issue: `Invalid answer format: "${question.correct_answer}"`,
          answer: question.correct_answer,
          options: question.options
        });
      }

      // Analyze options format
      if (options && options.A && options.B && options.C && options.D) {
        letterOptions++;
      } else if (options && options['0'] && options['1'] && options['2'] && options['3']) {
        numericOptions++;
      } else {
        problematicQuestions.push({
          id: question.id,
          issue: 'Invalid options format',
          answer: question.correct_answer,
          options: question.options
        });
      }
    });

    console.log('\n📈 Answer Format Analysis:');
    console.log(`   Letter format (A,B,C,D): ${letterFormat} questions`);
    console.log(`   Numeric format (0,1,2,3): ${numericFormat} questions`);
    console.log(`   Other/Invalid format: ${otherFormat} questions`);

    console.log('\n📈 Options Format Analysis:');
    console.log(`   Letter keys (A,B,C,D): ${letterOptions} questions`);
    console.log(`   Numeric keys (0,1,2,3): ${numericOptions} questions`);

    if (problematicQuestions.length > 0) {
      console.log(`\n⚠️  Found ${problematicQuestions.length} problematic questions:`);
      problematicQuestions.slice(0, 5).forEach(q => {
        console.log(`   - ID: ${q.id}, Issue: ${q.issue}`);
      });
      if (problematicQuestions.length > 5) {
        console.log(`   ... and ${problematicQuestions.length - 5} more`);
      }
    }

    return {
      total: totalQuestions,
      needsAnswerConversion: numericFormat,
      needsOptionsConversion: numericOptions,
      problematic: problematicQuestions.length
    };

  } catch (error) {
    console.error('❌ Error analyzing current state:', error.message);
    throw error;
  }
}

/**
 * Performs the bulk correction of quiz questions
 */
async function performBulkCorrection() {
  console.log('\n🔧 Starting bulk correction process...\n');

  try {
    // Fetch all questions that need correction
    const { data: questions, error: fetchError } = await supabase
      .from('questions')
      .select('id, question_text, correct_answer, options');

    if (fetchError) {
      throw new Error(`Failed to fetch questions: ${fetchError.message}`);
    }

    console.log(`📋 Processing ${questions.length} questions...`);

    let correctedCount = 0;
    let errorCount = 0;
    const batchSize = 50; // Process in batches to avoid overwhelming the database

    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize);
      console.log(`\n🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(questions.length / batchSize)} (${batch.length} questions)...`);

      const updates = [];

      for (const question of batch) {
        try {
          const originalAnswer = question.correct_answer;
          const originalOptions = question.options;

          // Convert to standardized format
          const standardizedAnswer = convertAnswerToLetterFormat(originalAnswer);
          const standardizedOptions = convertOptionsToLetterFormat(originalOptions);

          // Check if conversion is needed
          const needsUpdate = 
            originalAnswer !== standardizedAnswer || 
            JSON.stringify(originalOptions) !== JSON.stringify(standardizedOptions);

          if (needsUpdate) {
            updates.push({
              id: question.id,
              correct_answer: standardizedAnswer,
              options: standardizedOptions,
              updated_at: new Date().toISOString()
            });
          }

        } catch (error) {
          console.error(`❌ Error processing question ${question.id}:`, error.message);
          errorCount++;
        }
      }

      // Apply batch updates
      if (updates.length > 0) {
        console.log(`   📝 Updating ${updates.length} questions in this batch...`);

        for (const update of updates) {
          const { error: updateError } = await supabase
            .from('questions')
            .update({
              correct_answer: update.correct_answer,
              options: update.options,
              updated_at: update.updated_at
            })
            .eq('id', update.id);

          if (updateError) {
            console.error(`❌ Failed to update question ${update.id}:`, updateError.message);
            errorCount++;
          } else {
            correctedCount++;
          }
        }

        console.log(`   ✅ Successfully updated ${updates.length} questions`);
      } else {
        console.log(`   ℹ️  No updates needed for this batch`);
      }

      // Small delay between batches to be gentle on the database
      if (i + batchSize < questions.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    console.log(`\n🎉 Bulk correction completed!`);
    console.log(`   ✅ Successfully corrected: ${correctedCount} questions`);
    console.log(`   ❌ Errors encountered: ${errorCount} questions`);

    return { corrected: correctedCount, errors: errorCount };

  } catch (error) {
    console.error('❌ Error during bulk correction:', error.message);
    throw error;
  }
}

/**
 * Verifies the correction results
 */
async function verifyCorrection() {
  console.log('\n🔍 Verifying correction results...\n');

  const analysis = await analyzeCurrentState();

  console.log('\n📊 Post-Correction Summary:');
  console.log(`   Total questions: ${analysis.total}`);
  console.log(`   Questions needing answer conversion: ${analysis.needsAnswerConversion}`);
  console.log(`   Questions needing options conversion: ${analysis.needsOptionsConversion}`);
  console.log(`   Problematic questions remaining: ${analysis.problematic}`);

  if (analysis.needsAnswerConversion === 0 && analysis.needsOptionsConversion === 0) {
    console.log('\n✅ All questions have been successfully standardized!');
  } else {
    console.log('\n⚠️  Some questions still need attention. Consider running the script again.');
  }

  return analysis;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Bulk Quiz Correction Script\n');
  console.log('This script will standardize all quiz questions to use letter format (A,B,C,D)\n');

  try {
    // Step 1: Analyze current state
    const initialAnalysis = await analyzeCurrentState();

    if (initialAnalysis.needsAnswerConversion === 0 && initialAnalysis.needsOptionsConversion === 0) {
      console.log('\n✅ All questions are already in standardized format. No correction needed!');
      return;
    }

    // Step 2: Confirm before proceeding
    console.log('\n⚠️  This script will modify quiz questions in the database.');
    console.log('   Make sure you have a backup before proceeding.');
    console.log('\n🔄 Proceeding with bulk correction...');

    // Step 3: Perform bulk correction
    const correctionResults = await performBulkCorrection();

    // Step 4: Verify results
    await verifyCorrection();

    console.log('\n🎉 Bulk quiz correction completed successfully!');
    console.log('\n📝 Summary:');
    console.log(`   - Questions corrected: ${correctionResults.corrected}`);
    console.log(`   - Errors encountered: ${correctionResults.errors}`);
    console.log(`   - All quizzes should now work correctly with standardized answer validation`);

  } catch (error) {
    console.error('\n💥 Script failed:', error.message);
    console.error('\nPlease check the error details and try again.');
    process.exit(1);
  }
}

// Run the script
main();
