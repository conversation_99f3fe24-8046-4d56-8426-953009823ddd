/**
 * API Health Checker Utility
 * Checks if the backend API is available and healthy
 */

interface ApiHealthStatus {
  isAvailable: boolean;
  url: string;
  status?: number;
  error?: string;
  timestamp: string;
}

/**
 * Check if the API endpoint is available and healthy
 */
export async function checkApiHealth(apiUrl?: string): Promise<ApiHealthStatus> {
  const url = apiUrl || import.meta.env.VITE_API_URL || '';
  const timestamp = new Date().toISOString();

  // If no API URL is configured, return unavailable
  if (!url || url === 'https://www.secquiz.app') {
    return {
      isAvailable: false,
      url,
      error: 'API URL not configured or pointing to production without server',
      timestamp
    };
  }

  try {
    const healthEndpoint = `${url}/api/health`;
    const response = await fetch(healthEndpoint, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Short timeout for health check
      signal: AbortSignal.timeout(5000)
    });

    return {
      isAvailable: response.ok,
      url,
      status: response.status,
      error: response.ok ? undefined : `HTTP ${response.status}`,
      timestamp
    };
  } catch (error) {
    return {
      isAvailable: false,
      url,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    };
  }
}

/**
 * Check if a specific API endpoint is available
 */
export async function checkApiEndpoint(endpoint: string, apiUrl?: string): Promise<ApiHealthStatus> {
  const url = apiUrl || import.meta.env.VITE_API_URL || '';
  const timestamp = new Date().toISOString();

  if (!url) {
    return {
      isAvailable: false,
      url,
      error: 'API URL not configured',
      timestamp
    };
  }

  try {
    const fullUrl = `${url}${endpoint}`;
    const response = await fetch(fullUrl, {
      method: 'HEAD', // Use HEAD to avoid triggering actual operations
      signal: AbortSignal.timeout(3000)
    });

    return {
      isAvailable: response.status < 500, // Accept 4xx as "available but may have auth issues"
      url: fullUrl,
      status: response.status,
      timestamp
    };
  } catch (error) {
    return {
      isAvailable: false,
      url: `${url}${endpoint}`,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp
    };
  }
}

/**
 * Get the appropriate API URL for the current environment
 */
export function getApiUrl(): string {
  const envApiUrl = import.meta.env.VITE_API_URL;
  
  // In development, default to localhost if not specified
  if (import.meta.env.DEV && !envApiUrl) {
    return 'http://localhost:5000';
  }
  
  // In production, handle missing API URL gracefully
  if (!envApiUrl) {
    // Only show warning in development mode to avoid console spam
    if (import.meta.env.DEV) {
      console.info('VITE_API_URL not configured. Using Supabase direct operations instead of API endpoints.');
    }
    return '';
  }
  
  return envApiUrl;
}

/**
 * Check if we should use API endpoints or fallback to direct Supabase operations
 */
export async function shouldUseApiEndpoints(): Promise<boolean> {
  const apiUrl = getApiUrl();
  
  if (!apiUrl) {
    return false;
  }
  
  const health = await checkApiHealth(apiUrl);
  return health.isAvailable;
}

/**
 * Log API health status for debugging
 */
export function logApiHealthStatus(status: ApiHealthStatus, context?: string) {
  const prefix = context ? `[${context}]` : '[API Health]';
  
  if (status.isAvailable) {
    console.log(`${prefix} API is available at ${status.url}`);
  } else {
    console.warn(`${prefix} API unavailable at ${status.url}: ${status.error}`);
  }
}
