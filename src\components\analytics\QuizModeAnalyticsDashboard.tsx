/**
 * Quiz Mode Analytics Dashboard
 * Displays analytics data about quiz mode usage and performance
 */

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, TrendingUp, Clock, Target, Smartphone, Monitor } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { QuizAnalyticsService, ModeUsageStats } from '@/services/quiz-analytics-service';

interface AnalyticsDashboardProps {
  className?: string;
}

export const QuizModeAnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ className }) => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [modeStats, setModeStats] = useState<ModeUsageStats[]>([]);
  const [devicePatterns, setDevicePatterns] = useState<{
    mobile: { preferredModes: string[]; averageSessionTime: number };
    desktop: { preferredModes: string[]; averageSessionTime: number };
  }>({
    mobile: { preferredModes: [], averageSessionTime: 0 },
    desktop: { preferredModes: [], averageSessionTime: 0 }
  });

  useEffect(() => {
    loadAnalytics();
  }, [user]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      
      const [stats, patterns] = await Promise.all([
        QuizAnalyticsService.getModeUsageStats(user, 30),
        QuizAnalyticsService.getDeviceUsagePatterns(user)
      ]);

      setModeStats(stats);
      setDevicePatterns(patterns);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getModeColor = (mode: string) => {
    switch (mode) {
      case 'quick': return 'bg-blue-500';
      case 'standard': return 'bg-green-500';
      case 'comprehensive': return 'bg-orange-500';
      case 'complete': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getModeLabel = (mode: string) => {
    switch (mode) {
      case 'quick': return 'Quick Practice';
      case 'standard': return 'Standard Quiz';
      case 'comprehensive': return 'Comprehensive Review';
      case 'complete': return 'Complete Assessment';
      default: return mode;
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  if (loading) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-muted-foreground animate-pulse" />
            <p className="text-sm text-muted-foreground">Loading analytics...</p>
          </div>
        </div>
      </Card>
    );
  }

  if (modeStats.length === 0) {
    return (
      <Card className={`p-6 ${className}`}>
        <div className="flex items-center justify-center h-32">
          <div className="text-center">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">No analytics data available yet.</p>
            <p className="text-xs text-muted-foreground mt-1">Complete some quizzes to see your patterns!</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="h-5 w-5 text-cyber-primary" />
        <h3 className="text-lg font-semibold">Quiz Mode Analytics</h3>
        <Badge variant="secondary" className="text-xs">Last 30 days</Badge>
      </div>

      <Tabs defaultValue="modes" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="modes">Mode Usage</TabsTrigger>
          <TabsTrigger value="devices">Device Patterns</TabsTrigger>
        </TabsList>

        <TabsContent value="modes" className="space-y-4">
          {/* Mode Usage Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {modeStats.map((stat) => (
              <Card key={stat.mode} className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${getModeColor(stat.mode)}`} />
                    <h4 className="font-medium text-sm">{getModeLabel(stat.mode)}</h4>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {stat.usageCount} uses
                  </Badge>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span>Completion Rate</span>
                      <span>{Math.round(stat.averageCompletionRate)}%</span>
                    </div>
                    <Progress value={stat.averageCompletionRate} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between text-xs mb-1">
                      <span>Average Score</span>
                      <span>{Math.round(stat.averageScore)}%</span>
                    </div>
                    <Progress value={stat.averageScore} className="h-2" />
                  </div>

                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>Avg. Time: {formatTime(Math.round(stat.averageTimeSpent))}</span>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Overall Performance Summary */}
          <Card className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
            <div className="flex items-center gap-2 mb-3">
              <Target className="h-4 w-4 text-blue-600" />
              <h4 className="font-medium text-sm">Performance Insights</h4>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-blue-600">
                  {modeStats.reduce((sum, stat) => sum + stat.usageCount, 0)}
                </div>
                <div className="text-xs text-muted-foreground">Total Quizzes</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-green-600">
                  {Math.round(modeStats.reduce((sum, stat) => sum + stat.averageCompletionRate, 0) / modeStats.length)}%
                </div>
                <div className="text-xs text-muted-foreground">Avg. Completion</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-orange-600">
                  {Math.round(modeStats.reduce((sum, stat) => sum + stat.averageScore, 0) / modeStats.length)}%
                </div>
                <div className="text-xs text-muted-foreground">Avg. Score</div>
              </div>
              
              <div>
                <div className="text-lg font-bold text-purple-600">
                  {formatTime(Math.round(modeStats.reduce((sum, stat) => sum + stat.averageTimeSpent, 0) / modeStats.length))}
                </div>
                <div className="text-xs text-muted-foreground">Avg. Time</div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="devices" className="space-y-4">
          {/* Device Usage Patterns */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Smartphone className="h-4 w-4 text-blue-600" />
                <h4 className="font-medium text-sm">Mobile Usage</h4>
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-xs text-muted-foreground mb-2">Preferred Modes</div>
                  <div className="flex flex-wrap gap-1">
                    {devicePatterns.mobile.preferredModes.slice(0, 3).map((mode, index) => (
                      <Badge key={mode} variant={index === 0 ? "default" : "secondary"} className="text-xs">
                        {getModeLabel(mode)}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>Avg. Session: {formatTime(Math.round(devicePatterns.mobile.averageSessionTime))}</span>
                </div>
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Monitor className="h-4 w-4 text-green-600" />
                <h4 className="font-medium text-sm">Desktop Usage</h4>
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="text-xs text-muted-foreground mb-2">Preferred Modes</div>
                  <div className="flex flex-wrap gap-1">
                    {devicePatterns.desktop.preferredModes.slice(0, 3).map((mode, index) => (
                      <Badge key={mode} variant={index === 0 ? "default" : "secondary"} className="text-xs">
                        {getModeLabel(mode)}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>Avg. Session: {formatTime(Math.round(devicePatterns.desktop.averageSessionTime))}</span>
                </div>
              </div>
            </Card>
          </div>

          {/* Device Comparison */}
          <Card className="p-4 bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
            <div className="flex items-center gap-2 mb-3">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <h4 className="font-medium text-sm">Device Insights</h4>
            </div>
            
            <div className="text-sm text-muted-foreground">
              {devicePatterns.mobile.averageSessionTime > devicePatterns.desktop.averageSessionTime ? (
                <p>📱 You tend to spend more time on mobile sessions. Consider using comprehensive modes on mobile for deeper learning.</p>
              ) : devicePatterns.desktop.averageSessionTime > devicePatterns.mobile.averageSessionTime ? (
                <p>💻 You prefer longer desktop sessions. Desktop is great for comprehensive reviews and detailed study.</p>
              ) : (
                <p>⚖️ You have balanced usage across devices. Great for flexible learning!</p>
              )}
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </Card>
  );
};

export default QuizModeAnalyticsDashboard;
