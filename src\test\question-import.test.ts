import { describe, it, expect } from 'vitest';

// Mock the parsing logic from AdminQuestionImport
function parseQuestions(importText: string, topicId: string) {
  const questions = [];
  const questionBlocks = importText.split("---").filter(block => block.trim());
  
  interface ImportedQuestion {
    topic_id: string;
    options: Record<string, string>;
    correct_answer: string;
    question_text?: string;
    explanation?: string;
  }
  
  for (const block of questionBlocks) {
    const lines = block.trim().split("\n").map(line => line.trim()).filter(line => line.length > 0);
    const questionObj: ImportedQuestion = {
      topic_id: topicId,
      options: {},
      correct_answer: "",
    };
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      if (trimmedLine.startsWith("Q:")) {
        questionObj.question_text = trimmedLine.substring(2).trim();
      } else if (trimmedLine.match(/^[A-D]:/)) {
        const optionKey = trimmedLine.substring(0, 1);
        const optionValue = trimmedLine.substring(2).trim();
        if (optionValue) {
          questionObj.options[optionKey] = optionValue;
        }
      } else if (trimmedLine.startsWith("Answer:")) {
        const answer = trimmedLine.substring(7).trim().toUpperCase();
        // Validate answer is A, B, C, or D
        if (['A', 'B', 'C', 'D'].includes(answer)) {
          questionObj.correct_answer = answer;
        }
      } else if (trimmedLine.startsWith("Explanation:")) {
        questionObj.explanation = trimmedLine.substring(12).trim();
      }
    }
    
    // Validate question has all required fields
    if (
      questionObj.question_text &&
      questionObj.question_text.length >= 10 &&
      Object.keys(questionObj.options).length >= 2 &&
      questionObj.correct_answer &&
      questionObj.options[questionObj.correct_answer] // Ensure the correct answer exists as an option
    ) {
      questions.push(questionObj);
    }
  }
  
  return questions;
}

describe('Question Import Parsing', () => {
  it('should parse sample Active Directory questions correctly', () => {
    const sampleText = `Q: TechCorp has 500 employees across three offices in different cities. They need a centralized system to manage user accounts, passwords, and access to resources. Which solution would best meet their needs?
A: Local user accounts on each computer
B: Active Directory Domain Services
C: Excel spreadsheet with user information
D: Individual password files on each server
Answer: B
Explanation: AD provides centralized management for large organizations.
---
Q: After installing the ADDS feature, what must be done to make the server functional as an Active Directory server?
A: Restart the computer three times
B: Promote the server to a Domain Controller
C: Install additional RAM
D: Configure firewall rules
Answer: B
Explanation: Promotion configures the server as a functional DC.
---`;

    const questions = parseQuestions(sampleText, 'test-topic-id');
    
    expect(questions).toHaveLength(2);
    
    // Test first question
    expect(questions[0].question_text).toBe('TechCorp has 500 employees across three offices in different cities. They need a centralized system to manage user accounts, passwords, and access to resources. Which solution would best meet their needs?');
    expect(questions[0].options.A).toBe('Local user accounts on each computer');
    expect(questions[0].options.B).toBe('Active Directory Domain Services');
    expect(questions[0].options.C).toBe('Excel spreadsheet with user information');
    expect(questions[0].options.D).toBe('Individual password files on each server');
    expect(questions[0].correct_answer).toBe('B');
    expect(questions[0].explanation).toBe('AD provides centralized management for large organizations.');
    
    // Test second question
    expect(questions[1].question_text).toBe('After installing the ADDS feature, what must be done to make the server functional as an Active Directory server?');
    expect(questions[1].options.A).toBe('Restart the computer three times');
    expect(questions[1].options.B).toBe('Promote the server to a Domain Controller');
    expect(questions[1].options.C).toBe('Install additional RAM');
    expect(questions[1].options.D).toBe('Configure firewall rules');
    expect(questions[1].correct_answer).toBe('B');
    expect(questions[1].explanation).toBe('Promotion configures the server as a functional DC.');
  });

  it('should handle malformed questions gracefully', () => {
    const malformedText = `Q: This is a question without options
Answer: A
---
Q: This question has options but no answer
A: Option A
B: Option B
---
Q: This question has everything
A: Option A
B: Option B
Answer: A
Explanation: This is correct
---`;

    const questions = parseQuestions(malformedText, 'test-topic-id');
    
    // Only the third question should be valid
    expect(questions).toHaveLength(1);
    expect(questions[0].question_text).toBe('This question has everything');
  });

  it('should validate answer options exist', () => {
    const invalidAnswerText = `Q: Question with invalid answer
A: Option A
B: Option B
Answer: C
Explanation: This answer doesn't exist as an option
---`;

    const questions = parseQuestions(invalidAnswerText, 'test-topic-id');
    
    // Should be rejected because answer C doesn't exist as an option
    expect(questions).toHaveLength(0);
  });
});
