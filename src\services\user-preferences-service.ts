/**
 * User Preferences Service
 * Handles user preferences including default quiz length
 */

import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';

export interface UserPreferences {
  defaultQuizLength: number;
  defaultQuizMode: 'quick' | 'standard' | 'comprehensive' | 'complete';
  autoStartQuiz: boolean;
  showQuizInstructions: boolean;
  preferredDifficulty?: 'easy' | 'medium' | 'hard';
  adaptiveRecommendations: boolean;
  perTopicOverrides: Record<string, {
    mode: 'quick' | 'standard' | 'comprehensive' | 'complete';
    length?: number;
    lastUsed: string;
  }>;
}

export const DEFAULT_PREFERENCES: UserPreferences = {
  defaultQuizLength: 15,
  defaultQuizMode: 'standard',
  autoStartQuiz: false,
  showQuizInstructions: true,
  preferredDifficulty: undefined,
  adaptiveRecommendations: true,
  perTopicOverrides: {}
};

const PREFERENCES_KEY_PREFIX = 'quiz_preferences';

/**
 * User Preferences Service Class
 * Manages user preferences with fallback to localStorage for guests
 */
export class UserPreferencesService {
  /**
   * Gets user preferences from database or localStorage
   * @param user - Current user (null for guests)
   * @returns Promise<UserPreferences> - User preferences
   */
  static async getUserPreferences(user: User | null): Promise<UserPreferences> {
    try {
      if (!user) {
        // For guests, use localStorage
        return this.getGuestPreferences();
      }

      // For authenticated users, try to get from database first
      const { data, error } = await supabase
        .from('settings')
        .select('setting_key, setting_value')
        .eq('setting_key', `${PREFERENCES_KEY_PREFIX}_${user.id}`)
        .single();

      if (error || !data) {
        // If no preferences in database, check localStorage for migration
        const localPrefs = this.getGuestPreferences();
        
        // Save to database for future use
        await this.saveUserPreferences(user, localPrefs);
        
        return localPrefs;
      }

      // Parse preferences from database
      const preferences = JSON.parse(data.setting_value || '{}');
      return { ...DEFAULT_PREFERENCES, ...preferences };

    } catch (error) {
      console.error('Error getting user preferences:', error);
      return DEFAULT_PREFERENCES;
    }
  }

  /**
   * Saves user preferences to database or localStorage
   * @param user - Current user (null for guests)
   * @param preferences - Preferences to save
   * @returns Promise<boolean> - Success status
   */
  static async saveUserPreferences(
    user: User | null, 
    preferences: Partial<UserPreferences>
  ): Promise<boolean> {
    try {
      const fullPreferences = { ...DEFAULT_PREFERENCES, ...preferences };

      if (!user) {
        // For guests, save to localStorage
        localStorage.setItem('quiz_preferences', JSON.stringify(fullPreferences));
        return true;
      }

      // For authenticated users, save to database
      const { error } = await supabase
        .from('settings')
        .upsert({
          setting_key: `${PREFERENCES_KEY_PREFIX}_${user.id}`,
          setting_value: JSON.stringify(fullPreferences),
          description: 'User quiz preferences',
          is_public: false
        });

      if (error) {
        console.error('Error saving user preferences to database:', error);
        // Fallback to localStorage
        localStorage.setItem('quiz_preferences', JSON.stringify(fullPreferences));
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error saving user preferences:', error);
      return false;
    }
  }

  /**
   * Gets default quiz length for user
   * @param user - Current user (null for guests)
   * @returns Promise<number> - Default quiz length
   */
  static async getDefaultQuizLength(user: User | null): Promise<number> {
    try {
      const preferences = await this.getUserPreferences(user);
      return preferences.defaultQuizLength;
    } catch (error) {
      console.error('Error getting default quiz length:', error);
      return DEFAULT_PREFERENCES.defaultQuizLength;
    }
  }

  /**
   * Sets default quiz length for user
   * @param user - Current user (null for guests)
   * @param length - Quiz length to set as default
   * @returns Promise<boolean> - Success status
   */
  static async setDefaultQuizLength(user: User | null, length: number): Promise<boolean> {
    try {
      // Validate quiz length
      if (length < 5 || length > 50) {
        console.error('Invalid quiz length:', length);
        return false;
      }

      const currentPreferences = await this.getUserPreferences(user);
      const updatedPreferences = {
        ...currentPreferences,
        defaultQuizLength: length
      };

      return await this.saveUserPreferences(user, updatedPreferences);

    } catch (error) {
      console.error('Error setting default quiz length:', error);
      return false;
    }
  }

  /**
   * Gets default quiz mode for user
   * @param user - Current user (null for guests)
   * @returns Promise<string> - Default quiz mode
   */
  static async getDefaultQuizMode(user: User | null): Promise<'quick' | 'standard' | 'comprehensive' | 'complete'> {
    try {
      const preferences = await this.getUserPreferences(user);
      return preferences.defaultQuizMode;
    } catch (error) {
      console.error('Error getting default quiz mode:', error);
      return DEFAULT_PREFERENCES.defaultQuizMode;
    }
  }

  /**
   * Sets default quiz mode for user
   * @param user - Current user (null for guests)
   * @param mode - Quiz mode to set as default
   * @returns Promise<boolean> - Success status
   */
  static async setDefaultQuizMode(user: User | null, mode: 'quick' | 'standard' | 'comprehensive' | 'complete'): Promise<boolean> {
    try {
      const currentPreferences = await this.getUserPreferences(user);
      const updatedPreferences = {
        ...currentPreferences,
        defaultQuizMode: mode
      };

      return await this.saveUserPreferences(user, updatedPreferences);

    } catch (error) {
      console.error('Error setting default quiz mode:', error);
      return false;
    }
  }

  /**
   * Gets per-topic mode override
   * @param user - Current user (null for guests)
   * @param topicId - Topic ID
   * @returns Promise<object | null> - Topic override or null
   */
  static async getTopicModeOverride(user: User | null, topicId: string): Promise<{
    mode: 'quick' | 'standard' | 'comprehensive' | 'complete';
    length?: number;
    lastUsed: string;
  } | null> {
    try {
      const preferences = await this.getUserPreferences(user);
      return preferences.perTopicOverrides[topicId] || null;
    } catch (error) {
      console.error('Error getting topic mode override:', error);
      return null;
    }
  }

  /**
   * Sets per-topic mode override
   * @param user - Current user (null for guests)
   * @param topicId - Topic ID
   * @param mode - Quiz mode for this topic
   * @param length - Optional specific length
   * @returns Promise<boolean> - Success status
   */
  static async setTopicModeOverride(
    user: User | null,
    topicId: string,
    mode: 'quick' | 'standard' | 'comprehensive' | 'complete',
    length?: number
  ): Promise<boolean> {
    try {
      const currentPreferences = await this.getUserPreferences(user);
      const updatedPreferences = {
        ...currentPreferences,
        perTopicOverrides: {
          ...currentPreferences.perTopicOverrides,
          [topicId]: {
            mode,
            length,
            lastUsed: new Date().toISOString()
          }
        }
      };

      return await this.saveUserPreferences(user, updatedPreferences);

    } catch (error) {
      console.error('Error setting topic mode override:', error);
      return false;
    }
  }

  /**
   * Gets smart mode recommendation based on user history and topic characteristics
   * @param user - Current user (null for guests)
   * @param topicId - Topic ID
   * @param availableQuestions - Number of available questions
   * @returns Promise<object> - Recommendation with mode and reasoning
   */
  static async getSmartModeRecommendation(
    user: User | null,
    topicId: string,
    availableQuestions: number
  ): Promise<{
    mode: 'quick' | 'standard' | 'comprehensive' | 'complete';
    reasoning: string;
    confidence: 'high' | 'medium' | 'low';
  }> {
    try {
      const preferences = await this.getUserPreferences(user);

      if (!preferences.adaptiveRecommendations) {
        return {
          mode: preferences.defaultQuizMode,
          reasoning: 'Using your default preference',
          confidence: 'high'
        };
      }

      // Check for topic-specific override
      const topicOverride = preferences.perTopicOverrides[topicId];
      if (topicOverride) {
        return {
          mode: topicOverride.mode,
          reasoning: 'Based on your previous choice for this topic',
          confidence: 'high'
        };
      }

      // Smart recommendation based on available questions
      if (availableQuestions < 10) {
        return {
          mode: 'quick',
          reasoning: 'Limited questions available - quick mode recommended',
          confidence: 'high'
        };
      } else if (availableQuestions < 20) {
        return {
          mode: 'standard',
          reasoning: 'Moderate question pool - standard mode recommended',
          confidence: 'medium'
        };
      } else if (availableQuestions >= 50) {
        return {
          mode: 'comprehensive',
          reasoning: 'Large question pool - comprehensive mode recommended',
          confidence: 'high'
        };
      } else {
        return {
          mode: preferences.defaultQuizMode,
          reasoning: 'Based on your default preference',
          confidence: 'medium'
        };
      }

    } catch (error) {
      console.error('Error getting smart mode recommendation:', error);
      return {
        mode: 'standard',
        reasoning: 'Default recommendation',
        confidence: 'low'
      };
    }
  }

  /**
   * Gets preferences from localStorage (for guests or fallback)
   * @returns UserPreferences - Preferences from localStorage or defaults
   */
  private static getGuestPreferences(): UserPreferences {
    try {
      const stored = localStorage.getItem('quiz_preferences');
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_PREFERENCES, ...parsed };
      }
    } catch (error) {
      console.error('Error parsing localStorage preferences:', error);
    }
    
    return DEFAULT_PREFERENCES;
  }

  /**
   * Migrates preferences from localStorage to database for newly authenticated users
   * @param user - Newly authenticated user
   * @returns Promise<boolean> - Success status
   */
  static async migrateGuestPreferences(user: User): Promise<boolean> {
    try {
      // Check if user already has preferences in database
      const { data: existingPrefs } = await supabase
        .from('settings')
        .select('setting_key')
        .eq('setting_key', `${PREFERENCES_KEY_PREFIX}_${user.id}`)
        .single();

      if (existingPrefs) {
        // User already has preferences, no migration needed
        return true;
      }

      // Get preferences from localStorage
      const guestPrefs = this.getGuestPreferences();
      
      // Save to database
      const success = await this.saveUserPreferences(user, guestPrefs);
      
      if (success) {
        console.log('Successfully migrated guest preferences to user account');
      }
      
      return success;

    } catch (error) {
      console.error('Error migrating guest preferences:', error);
      return false;
    }
  }

  /**
   * Resets user preferences to defaults
   * @param user - Current user (null for guests)
   * @returns Promise<boolean> - Success status
   */
  static async resetPreferences(user: User | null): Promise<boolean> {
    try {
      return await this.saveUserPreferences(user, DEFAULT_PREFERENCES);
    } catch (error) {
      console.error('Error resetting preferences:', error);
      return false;
    }
  }

  /**
   * Gets all available quiz length options with validation
   * @param availableQuestions - Number of questions available for the topic
   * @returns Array of valid quiz length options
   */
  static getValidQuizLengthOptions(availableQuestions: number): number[] {
    const allOptions = [10, 15, 20, 25];
    
    if (availableQuestions === 0) {
      return [];
    }
    
    // Return options that are available, plus the exact number if it's not in the standard options
    const validOptions = allOptions.filter(option => option <= availableQuestions);
    
    // If available questions is not in standard options and is reasonable, add it
    if (!allOptions.includes(availableQuestions) && 
        availableQuestions >= 5 && 
        availableQuestions <= 50) {
      validOptions.push(availableQuestions);
      validOptions.sort((a, b) => a - b);
    }
    
    return validOptions;
  }

  /**
   * Validates quiz length against available questions and user limits
   * @param requestedLength - Requested quiz length
   * @param availableQuestions - Available questions for the topic
   * @param user - Current user for premium checks
   * @returns Validation result with adjusted length if needed
   */
  static validateQuizLength(
    requestedLength: number,
    availableQuestions: number,
    user: User | null = null
  ): {
    isValid: boolean;
    adjustedLength: number;
    message?: string;
    severity: 'info' | 'warning' | 'error';
  } {
    // Check if any questions are available
    if (availableQuestions === 0) {
      return {
        isValid: false,
        adjustedLength: 0,
        message: 'No questions available for this topic.',
        severity: 'error'
      };
    }

    // Check if requested length is within reasonable bounds
    if (requestedLength < 1) {
      return {
        isValid: false,
        adjustedLength: Math.min(10, availableQuestions),
        message: 'Quiz length must be at least 1 question.',
        severity: 'error'
      };
    }

    if (requestedLength > 50) {
      return {
        isValid: false,
        adjustedLength: Math.min(25, availableQuestions),
        message: 'Quiz length cannot exceed 50 questions.',
        severity: 'error'
      };
    }

    // Adjust length if more questions requested than available
    if (requestedLength > availableQuestions) {
      return {
        isValid: true,
        adjustedLength: availableQuestions,
        message: `Only ${availableQuestions} questions available. Quiz adjusted to ${availableQuestions} questions.`,
        severity: 'warning'
      };
    }

    // Check for optimal question variety
    if (availableQuestions < 20) {
      return {
        isValid: true,
        adjustedLength: requestedLength,
        message: `This topic has limited questions (${availableQuestions}). Consider adding more questions for better variety.`,
        severity: 'info'
      };
    }

    // All good
    return {
      isValid: true,
      adjustedLength: requestedLength,
      severity: 'info'
    };
  }
}

export default UserPreferencesService;