/**
 * Quiz Setup Page
 * Allows users to configure quiz settings before starting
 */

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, Settings, BookOpen } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/use-auth';
import Navbar from '@/components/Navbar';
import BottomNavigation from '@/components/BottomNavigation';
import { QuizLengthSelector, QUIZ_LENGTH_OPTIONS, QUIZ_LENGTH_MODES, QuizLengthMode } from '@/components/quiz/QuizLengthSelector';
import { QuizRandomizationService } from '@/services/quiz-randomization-service';
import { UserPreferencesService } from '@/services/user-preferences-service';
import { QuizAnalyticsService } from '@/services/quiz-analytics-service';
import { supabase } from '@/integrations/supabase/client';
import { canAccessTopic, isTopicPremium } from '@/utils/topic-access';

interface TopicInfo {
  id: string;
  title: string;
  description: string;
  difficulty: string;
  is_premium: boolean;
}

const QuizSetupPage = () => {
  const { topicId } = useParams<{ topicId: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // State
  const [loading, setLoading] = useState(true);
  const [startingQuiz, setStartingQuiz] = useState(false);
  const [topic, setTopic] = useState<TopicInfo | null>(null);
  const [availableQuestions, setAvailableQuestions] = useState(0);
  const [selectedMode, setSelectedMode] = useState<QuizLengthMode['id']>('standard');
  const [selectedLength, setSelectedLength] = useState(15); // Legacy support
  const [saveAsDefault, setSaveAsDefault] = useState(false);

  // Load topic information and user preferences
  useEffect(() => {
    const loadTopicAndPreferences = async () => {
      try {
        setLoading(true);

        if (!topicId) {
          toast({
            title: 'Error',
            description: 'No topic specified.',
            variant: 'destructive'
          });
          navigate('/quizzes');
          return;
        }

        // Fetch topic information
        const { data: topicData, error: topicError } = await supabase
          .from('topics')
          .select('*')
          .eq('id', topicId)
          .single();

        if (topicError || !topicData) {
          toast({
            title: 'Topic Not Found',
            description: 'The requested quiz topic could not be found.',
            variant: 'destructive'
          });
          navigate('/quizzes');
          return;
        }

        // Check access permissions
        const hasAccess = await canAccessTopic(topicData.title, topicData.id, user);
        if (!hasAccess) {
          toast({
            title: 'Access Denied',
            description: 'You do not have permission to access this quiz.',
            variant: 'destructive'
          });
          navigate('/quizzes');
          return;
        }

        // Set topic info
        const topicInfo: TopicInfo = {
          id: topicData.id,
          title: topicData.title,
          description: topicData.description || '',
          difficulty: topicData.difficulty || 'medium',
          is_premium: isTopicPremium(topicData.title, topicData.difficulty)
        };
        setTopic(topicInfo);

        // Get question statistics
        const stats = await QuizRandomizationService.getTopicQuestionStats(topicId);
        setAvailableQuestions(stats.total_questions);

        // Load user's default quiz length preference and determine best mode
        const defaultLength = await UserPreferencesService.getDefaultQuizLength(user);

        // Find the best matching mode for the default length
        let bestMode: QuizLengthMode['id'] = 'standard';
        let closestDiff = Infinity;

        for (const mode of QUIZ_LENGTH_MODES) {
          const modeLength = mode.getValue(stats.total_questions);
          const diff = Math.abs(modeLength - defaultLength);
          if (diff < closestDiff) {
            closestDiff = diff;
            bestMode = mode.id;
          }
        }

        setSelectedMode(bestMode);

        // Set the actual length based on the selected mode
        const selectedModeObj = QUIZ_LENGTH_MODES.find(m => m.id === bestMode);
        const effectiveLength = selectedModeObj?.getValue(stats.total_questions) || defaultLength;
        setSelectedLength(effectiveLength);

        // Show info if questions are limited
        if (stats.total_questions < 20) {
          toast({
            title: 'Limited Question Pool',
            description: `This topic has ${stats.total_questions} questions. Quiz length automatically optimized.`,
            variant: 'default'
          });
        }

      } catch (error) {
        console.error('Error loading topic and preferences:', error);
        toast({
          title: 'Error',
          description: 'Failed to load quiz setup information.',
          variant: 'destructive'
        });
        navigate('/quizzes');
      } finally {
        setLoading(false);
      }
    };

    loadTopicAndPreferences();
  }, [topicId, user, toast, navigate]);

  const handleLengthChange = (length: number) => {
    setSelectedLength(length);
  };

  const handleModeChange = (mode: QuizLengthMode['id'], length: number) => {
    setSelectedMode(mode);
    setSelectedLength(length);
  };

  const handleStartQuiz = async () => {
    try {
      setStartingQuiz(true);

      // Track analytics for mode selection
      await QuizAnalyticsService.trackModeSelection(
        user,
        selectedMode,
        topicId!,
        selectedLength
      );

      // Save as default preference if requested
      if (saveAsDefault) {
        await UserPreferencesService.setDefaultQuizMode(user, selectedMode);
        await UserPreferencesService.setDefaultQuizLength(user, selectedLength);
        toast({
          title: 'Preference Saved',
          description: `"${QUIZ_LENGTH_MODES.find(m => m.id === selectedMode)?.label}" mode saved as default.`,
          variant: 'default'
        });
      }

      // Save topic-specific override if different from default
      const defaultMode = await UserPreferencesService.getDefaultQuizMode(user);
      if (selectedMode !== defaultMode) {
        await UserPreferencesService.setTopicModeOverride(user, topicId!, selectedMode, selectedLength);
      }

      // Navigate to quiz page with the selected length as a parameter
      navigate(`/quiz/${topicId}?length=${selectedLength}&mode=${selectedMode}`);

    } catch (error) {
      console.error('Error starting quiz:', error);
      toast({
        title: 'Error',
        description: 'Failed to start quiz. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setStartingQuiz(false);
    }
  };

  const handleBackToQuizzes = () => {
    navigate('/quizzes');
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 text-cyber-primary animate-spin mb-4" />
            <p className="text-muted-foreground">Loading quiz setup...</p>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  if (!topic) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <h1 className="text-2xl font-bold mb-2">Topic Not Found</h1>
          <p className="text-center text-muted-foreground mb-8">
            The requested quiz topic could not be found.
          </p>
          <Button onClick={handleBackToQuizzes}>
            Back to Quizzes
          </Button>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
      <Navbar />
      
      <div className="flex-1 container py-8 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToQuizzes}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Quizzes
            </Button>
          </div>

          {/* Topic Information */}
          <Card className="p-6 mb-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 rounded-lg bg-cyber-primary/10 flex items-center justify-center">
                  <BookOpen className="h-6 w-6 text-cyber-primary" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">{topic.title}</h1>
                  <p className="text-muted-foreground">{topic.description}</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge variant={topic.difficulty === 'easy' ? 'default' : topic.difficulty === 'medium' ? 'secondary' : 'destructive'}>
                  {topic.difficulty}
                </Badge>
                {topic.is_premium && (
                  <Badge variant="outline" className="border-cyber-primary text-cyber-primary">
                    Premium
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="text-sm text-muted-foreground">
              <p>Available Questions: {availableQuestions}</p>
            </div>
          </Card>

          {/* Quiz Setup */}
          <Card className="p-6 mb-6">
            <div className="flex items-center gap-2 mb-6">
              <Settings className="h-5 w-5 text-cyber-primary" />
              <h2 className="text-xl font-semibold">Quiz Setup</h2>
            </div>

            <QuizLengthSelector
              availableQuestions={availableQuestions}
              selectedMode={selectedMode}
              selectedLength={selectedLength}
              onModeChange={handleModeChange}
              onLengthChange={handleLengthChange}
              onStartQuiz={handleStartQuiz}
              loading={startingQuiz}
              useModesOnly={true}
              showQuestionPool={true}
            />

            {/* Save as Default Option */}
            {user && (
              <div className="mt-6 pt-6 border-t">
                <label className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={saveAsDefault}
                    onChange={(e) => setSaveAsDefault(e.target.checked)}
                    className="rounded border-gray-300 text-cyber-primary focus:ring-cyber-primary"
                  />
                  <span className="text-sm">Save "{QUIZ_LENGTH_MODES.find(m => m.id === selectedMode)?.label}" mode as my default</span>
                </label>
              </div>
            )}
          </Card>

          {/* Quick Start with Modes */}
          <Card className="p-6">
            <h3 className="font-semibold mb-4">Quick Start</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start immediately with your preferred quiz mode
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
              {QUIZ_LENGTH_MODES.map((mode) => {
                const length = mode.getValue(availableQuestions);
                const isSelected = selectedMode === mode.id;

                return (
                  <Button
                    key={mode.id}
                    variant={isSelected ? 'default' : 'outline'}
                    size="sm"
                    disabled={startingQuiz}
                    onClick={() => {
                      handleModeChange(mode.id, length);
                      handleStartQuiz();
                    }}
                    className="flex flex-col h-auto py-3"
                  >
                    <span className="font-medium text-xs">
                      {mode.label}
                    </span>
                    <span className="text-xs opacity-75">
                      {length} questions
                    </span>
                  </Button>
                );
              })}
            </div>
          </Card>
        </div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default QuizSetupPage;