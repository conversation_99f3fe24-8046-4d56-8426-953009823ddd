-- Create topic-domain many-to-many relationship
-- This migration creates a junction table to establish many-to-many relationships between topics and domains

-- Create the junction table for topic-domain relationships
CREATE TABLE IF NOT EXISTS public.topic_domains (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  topic_id UUID NOT NULL REFERENCES public.topics(id) ON DELETE CASCADE,
  domain_id UUID NOT NULL REFERENCES public.domains(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  
  -- Ensure unique combinations of topic and domain
  UNIQUE(topic_id, domain_id)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_topic_domains_topic_id ON public.topic_domains(topic_id);
CREATE INDEX IF NOT EXISTS idx_topic_domains_domain_id ON public.topic_domains(domain_id);
CREATE INDEX IF NOT EXISTS idx_topic_domains_created_at ON public.topic_domains(created_at);

-- Enable Row Level Security
ALTER TABLE public.topic_domains ENABLE ROW LEVEL SECURITY;

-- Create policies for topic_domains table
-- Allow everyone to read topic-domain mappings (for filtering topics by domain)
CREATE POLICY "Topic-domain mappings are viewable by everyone"
  ON public.topic_domains
  FOR SELECT
  USING (true);

-- Only admins can insert/update/delete topic-domain mappings
CREATE POLICY "Topic-domain mappings are editable by admins"
  ON public.topic_domains
  FOR ALL
  USING (
    auth.uid() IN (
      SELECT user_id FROM admin_users WHERE is_admin = true
    )
  );

-- Add helpful comments
COMMENT ON TABLE public.topic_domains IS 'Junction table for many-to-many relationship between topics and domains';
COMMENT ON COLUMN public.topic_domains.topic_id IS 'Reference to the topic';
COMMENT ON COLUMN public.topic_domains.domain_id IS 'Reference to the domain';
COMMENT ON COLUMN public.topic_domains.created_at IS 'When this mapping was created';
COMMENT ON COLUMN public.topic_domains.created_by IS 'Admin user who created this mapping';

-- Create a function to get topics by domain
CREATE OR REPLACE FUNCTION public.get_topics_by_domain(domain_id_param UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  icon TEXT,
  difficulty TEXT,
  is_active BOOLEAN,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.title,
    t.description,
    t.icon,
    t.difficulty,
    t.is_active,
    t.created_at,
    t.updated_at
  FROM public.topics t
  INNER JOIN public.topic_domains td ON t.id = td.topic_id
  WHERE td.domain_id = domain_id_param
    AND t.is_active = true
  ORDER BY t.title;
END;
$$;

-- Create a function to get domains by topic
CREATE OR REPLACE FUNCTION public.get_domains_by_topic(topic_id_param UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  slug TEXT,
  description TEXT,
  icon TEXT,
  color_theme TEXT,
  difficulty_level TEXT,
  is_active BOOLEAN
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.name,
    d.slug,
    d.description,
    d.icon,
    d.color_theme,
    d.difficulty_level,
    d.is_active
  FROM public.domains d
  INNER JOIN public.topic_domains td ON d.id = td.domain_id
  WHERE td.topic_id = topic_id_param
    AND d.is_active = true
  ORDER BY d.name;
END;
$$;

-- Create a function to check if a topic belongs to a domain
CREATE OR REPLACE FUNCTION public.topic_belongs_to_domain(topic_id_param UUID, domain_id_param UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.topic_domains 
    WHERE topic_id = topic_id_param 
      AND domain_id = domain_id_param
  );
END;
$$;

-- Grant necessary permissions
GRANT SELECT ON public.topic_domains TO anon, authenticated;
GRANT ALL ON public.topic_domains TO service_role;
GRANT EXECUTE ON FUNCTION public.get_topics_by_domain(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_domains_by_topic(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.topic_belongs_to_domain(UUID, UUID) TO anon, authenticated;
