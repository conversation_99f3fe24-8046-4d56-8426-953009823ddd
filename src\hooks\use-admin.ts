import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "./use-auth";
import { executeWithValidSession, isAuthError, getAuthErrorMessage } from "@/utils/session-manager";

export type Topic = {
  id: string;
  title: string;
  description: string | null;
  icon: string | null;
  is_active: boolean;
  difficulty: string;
  domain_id?: string;
  created_at: string;
  created_by: string | null;
  updated_at: string;
  domain?: {
    id: string;
    name: string;
    slug: string;
  };
};

export type Question = {
  id: string;
  topic_id: string | null;
  question_text: string;
  options: Record<string, string>;
  correct_answer: string;
  explanation: string | null;
  difficulty: string;
  created_at: string;
  created_by: string | null;
  updated_at: string;
};

export function useAdminTopics() {
  const { user } = useAuth();
  const [topics, setTopics] = useState<Topic[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTopics = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try direct query first (topics are public data)
      let result;

      try {
        // Direct query without authentication for public data
        result = await supabase
          .from("topics")
          .select(`
            *,
            domain:domains(id, name, slug)
          `)
          .order("created_at", { ascending: false });

        // If direct query works, use it
        if (!result.error) {
          setTopics(result.data || []);
          return;
        }
      } catch (directError) {
        console.warn("Direct query failed, trying with session management:", directError);
      }

      // Fallback: Use session manager with anonymous access allowed
      result = await executeWithValidSession(async () => {
        // Try with domain join first, fallback to topics only
        try {
          return await supabase
            .from("topics")
            .select(`
              *,
              domain:domains(id, name, slug)
            `)
            .order("created_at", { ascending: false });
        } catch (joinError) {
          console.warn("Error with domain join, falling back to topics only:", joinError);

          // Fallback: fetch topics without domain relationships
          return await supabase
            .from("topics")
            .select("*")
            .order("created_at", { ascending: false });
        }
      }, true); // Allow anonymous access

      if (result.error) {
        const errorMessage = isAuthError(result.error)
          ? getAuthErrorMessage(result.error)
          : result.error.message || "Failed to fetch topics";
        throw new Error(errorMessage);
      }

      setTopics(result.data || []);
    } catch (err: any) {
      console.error("Error fetching topics:", err);
      setError(err.message || "Failed to fetch topics");
    } finally {
      setLoading(false);
    }
  };

  const deleteTopic = async (id: string) => {
    try {
      const { error } = await supabase
        .from("topics")
        .delete()
        .eq("id", id);

      if (error) throw error;
      
      // Update the local state after successful deletion
      setTopics(topics.filter(topic => topic.id !== id));
      return { success: true };
    } catch (err) {
      console.error("Error deleting topic:", err);
      return { success: false, error: err.message };
    }
  };

  useEffect(() => {
    if (user) {
      fetchTopics();
    }
  }, [user]);

  return {
    topics,
    loading,
    error,
    refreshTopics: fetchTopics,
    deleteTopic
  };
}

export function useAdminQuestions(topicId?: string) {
  const { user } = useAuth();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuestions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Try direct query first (questions are public data for viewing)
      let result;

      try {
        let query = supabase
          .from("questions")
          .select("*")
          .order("created_at", { ascending: false });

        if (topicId) {
          query = query.eq("topic_id", topicId);
        }

        result = await query;

        // If direct query works, use it
        if (!result.error) {
          const data = result.data;
          const transformedData = (data || []).map(q => ({
            ...q,
            options: typeof q.options === 'string'
              ? JSON.parse(q.options)
              : q.options
          }));
          setQuestions(transformedData);
          return;
        }
      } catch (directError) {
        console.warn("Direct query failed, trying with session management:", directError);
      }

      // Fallback: Use session manager with anonymous access allowed
      result = await executeWithValidSession(async () => {
        let query = supabase
          .from("questions")
          .select("*")
          .order("created_at", { ascending: false });

        if (topicId) {
          query = query.eq("topic_id", topicId);
        }

        return await query;
      }, true); // Allow anonymous access

      if (result.error) {
        const errorMessage = isAuthError(result.error)
          ? getAuthErrorMessage(result.error)
          : result.error.message || "Failed to fetch questions";
        throw new Error(errorMessage);
      }

      const data = result.data;

      // Transform the options to ensure they're Record<string, string>
      const transformedData = (data || []).map(q => ({
        ...q,
        options: typeof q.options === 'string'
          ? JSON.parse(q.options)
          : q.options
      }));

      setQuestions(transformedData);
    } catch (err: any) {
      console.error("Error fetching questions:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [topicId]);

  const deleteQuestion = async (id: string) => {
    try {
      const { error } = await supabase
        .from("questions")
        .delete()
        .eq("id", id);

      if (error) throw error;
      
      // Update the local state after successful deletion
      setQuestions(questions.filter(question => question.id !== id));
      return { success: true };
    } catch (err) {
      console.error("Error deleting question:", err);
      return { success: false, error: err.message };
    }
  };

  useEffect(() => {
    if (user) {
      fetchQuestions();
    }
  }, [user, topicId, fetchQuestions]);

  return {
    questions,
    loading,
    error,
    refreshQuestions: fetchQuestions,
    deleteQuestion
  };
}
