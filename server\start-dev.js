// Simple development server startup script
// This bypasses TypeScript compilation issues for immediate testing

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Basic middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://127.0.0.1:5173'],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));

// Basic health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    supabase_configured: !!(process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY)
  });
});

// Basic config endpoint
app.get('/api/config', (req, res) => {
  res.json({
    supabase: {
      url: process.env.VITE_SUPABASE_URL,
      configured: !!process.env.VITE_SUPABASE_URL
    },
    paystack: {
      configured: !!process.env.VITE_PAYSTACK_PUBLIC_KEY
    },
    features: {
      adminFeatures: process.env.VITE_ENABLE_ADMIN_FEATURES === 'true',
      debugMode: process.env.VITE_ENABLE_DEBUG_MODE === 'true'
    }
  });
});

// Catch all for unhandled routes
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Route not found',
    path: req.originalUrl,
    method: req.method
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Development server running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`⚙️  Config check: http://localhost:${PORT}/api/config`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 Supabase URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Configured' : 'Not configured'}`);
  console.log(`🔑 Service Role Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Configured' : 'Not configured'}`);
});
