import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
  Loader2,
  Plus,
  Edit,
  Trash2,
  Search,
  FilterX,
  RefreshCw,
  ArrowLeft,
  BookOpen,
  Clock,
  Save,
  X,
  AlertTriangle,
  Settings
} from "lucide-react";
import BottomNavigation from "@/components/BottomNavigation";
import DesktopSideNav from "@/components/DesktopSideNav";
import MobileHeader from "@/components/MobileHeader";
import AdminTopicDomainAssignment from "@/components/AdminTopicDomainAssignment";
import { useNavigate } from "react-router-dom";
import { Domain } from "@/types/domain";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface DomainFormData {
  name: string;
  slug: string;
  description: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  estimated_duration_weeks: number;
  prerequisites: string[];
  is_active: boolean;
  sort_order: number;
}

const AdminDomainsPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [domains, setDomains] = useState<Domain[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedDomain, setSelectedDomain] = useState<Domain | null>(null);
  const [formData, setFormData] = useState<DomainFormData>({
    name: "",
    slug: "",
    description: "",
    difficulty_level: "beginner",
    estimated_duration_weeks: 4,
    prerequisites: [],
    is_active: true,
    sort_order: 0,
  });
  const [formLoading, setFormLoading] = useState(false);
  const [prerequisitesInput, setPrerequisitesInput] = useState("");

  // Predefined domain templates for common cybersecurity domains
  const domainTemplates = [
    {
      name: "Network Security",
      slug: "network-security",
      description: "Comprehensive network security concepts, protocols, and defense mechanisms",
      difficulty_level: "intermediate" as const,
      estimated_duration_weeks: 6,
      prerequisites: ["Basic networking", "Computer fundamentals"],
    },
    {
      name: "Cloud Security",
      slug: "cloud-security",
      description: "Cloud computing security, AWS/Azure/GCP security, and cloud-native protection",
      difficulty_level: "intermediate" as const,
      estimated_duration_weeks: 8,
      prerequisites: ["Basic cloud concepts", "Network security"],
    },
    {
      name: "Incident Response",
      slug: "incident-response",
      description: "Cybersecurity incident handling, forensics, and emergency response procedures",
      difficulty_level: "advanced" as const,
      estimated_duration_weeks: 10,
      prerequisites: ["Network security", "System administration"],
    },
    {
      name: "Penetration Testing",
      slug: "penetration-testing",
      description: "Ethical hacking, vulnerability assessment, and penetration testing methodologies",
      difficulty_level: "advanced" as const,
      estimated_duration_weeks: 10,
      prerequisites: ["Network security", "System administration", "Programming basics"],
    },
    {
      name: "Cybersecurity Foundations",
      slug: "cybersecurity-foundations",
      description: "Fundamental cybersecurity concepts, principles, and basic security practices",
      difficulty_level: "beginner" as const,
      estimated_duration_weeks: 4,
      prerequisites: [],
    },
    {
      name: "Ethical Hacking",
      slug: "ethical-hacking",
      description: "Ethical hacking, vulnerability assessment, and penetration testing methodologies",
      difficulty_level: "advanced" as const,
      estimated_duration_weeks: 10,
      prerequisites: ["Network security", "System administration", "Programming"],
    },
  ];

  // Fetch domains
  const fetchDomains = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("domains")
        .select("*")
        .order("sort_order");

      if (error) throw error;
      setDomains(data || []);
    } catch (error) {
      console.error("Error fetching domains:", error);
      toast({
        title: "Error fetching domains",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      description: "",
      difficulty_level: "beginner",
      estimated_duration_weeks: 4,
      prerequisites: [],
      is_active: true,
      sort_order: 0,
    });
    setPrerequisitesInput("");
  };

  const applyDomainTemplate = (template: typeof domainTemplates[0]) => {
    setFormData({
      name: template.name,
      slug: template.slug,
      description: template.description,
      difficulty_level: template.difficulty_level,
      estimated_duration_weeks: template.estimated_duration_weeks,
      prerequisites: template.prerequisites,
      is_active: true,
      sort_order: domains.length,
    });
    setPrerequisitesInput(template.prerequisites.join(', '));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  // Create domain
  const createDomain = async () => {
    try {
      setFormLoading(true);

      // Enhanced validation
      if (!formData.name.trim()) {
        toast({
          title: "Validation Error",
          description: "Domain name is required",
          variant: "destructive",
        });
        return;
      }

      if (formData.name.trim().length < 3) {
        toast({
          title: "Validation Error",
          description: "Domain name must be at least 3 characters long",
          variant: "destructive",
        });
        return;
      }

      // Generate slug if not provided
      const slug = formData.slug || generateSlug(formData.name);

      // Check for duplicate domain names or slugs
      const { data: existingDomains, error: checkError } = await supabase
        .from("domains")
        .select("name, slug")
        .or(`name.ilike.${formData.name.trim()},slug.eq.${slug}`);

      if (checkError) throw checkError;

      if (existingDomains && existingDomains.length > 0) {
        const duplicateName = existingDomains.find(d => d.name.toLowerCase() === formData.name.trim().toLowerCase());
        const duplicateSlug = existingDomains.find(d => d.slug === slug);

        if (duplicateName) {
          toast({
            title: "Validation Error",
            description: "A domain with this name already exists",
            variant: "destructive",
          });
          return;
        }

        if (duplicateSlug) {
          toast({
            title: "Validation Error",
            description: "A domain with this URL slug already exists",
            variant: "destructive",
          });
          return;
        }
      }

      // Parse prerequisites
      const prerequisites = prerequisitesInput
        .split(',')
        .map(p => p.trim())
        .filter(p => p.length > 0);

      const { data, error } = await supabase
        .from("domains")
        .insert([{
          ...formData,
          slug,
          prerequisites,
        }])
        .select()
        .single();

      if (error) throw error;

      setDomains(prev => [...prev, data]);
      setShowCreateDialog(false);
      resetForm();

      toast({
        title: "Domain created successfully",
        description: `${data.name} has been created and is ready for content`,
      });
    } catch (error: any) {
      console.error("Error creating domain:", error);
      toast({
        title: "Error creating domain",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setFormLoading(false);
    }
  };

  // Update domain
  const updateDomain = async () => {
    if (!selectedDomain) return;

    try {
      setFormLoading(true);

      // Validate form
      if (!formData.name.trim()) {
        toast({
          title: "Validation Error",
          description: "Domain name is required",
          variant: "destructive",
        });
        return;
      }

      // Parse prerequisites
      const prerequisites = prerequisitesInput
        .split(',')
        .map(p => p.trim())
        .filter(p => p.length > 0);

      const { data, error } = await supabase
        .from("domains")
        .update({
          ...formData,
          prerequisites,
        })
        .eq("id", selectedDomain.id)
        .select()
        .single();

      if (error) throw error;

      setDomains(prev => prev.map(d => d.id === selectedDomain.id ? data : d));
      setShowEditDialog(false);
      setSelectedDomain(null);
      resetForm();

      toast({
        title: "Domain updated",
        description: `${data.name} has been updated successfully`,
      });
    } catch (error: any) {
      console.error("Error updating domain:", error);
      toast({
        title: "Error updating domain",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setFormLoading(false);
    }
  };

  // Delete domain
  const deleteDomain = async () => {
    if (!selectedDomain) return;

    try {
      setFormLoading(true);

      const { error } = await supabase
        .from("domains")
        .delete()
        .eq("id", selectedDomain.id);

      if (error) throw error;

      setDomains(prev => prev.filter(d => d.id !== selectedDomain.id));
      setShowDeleteDialog(false);
      setSelectedDomain(null);

      toast({
        title: "Domain deleted",
        description: `${selectedDomain.name} has been deleted successfully`,
      });
    } catch (error: any) {
      console.error("Error deleting domain:", error);
      toast({
        title: "Error deleting domain",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setFormLoading(false);
    }
  };

  // Toggle domain status
  const toggleDomainStatus = async (domain: Domain) => {
    try {
      const { data, error } = await supabase
        .from("domains")
        .update({ is_active: !domain.is_active })
        .eq("id", domain.id)
        .select()
        .single();

      if (error) throw error;

      setDomains(prev => prev.map(d => d.id === domain.id ? data : d));

      toast({
        title: "Domain status updated",
        description: `${domain.name} is now ${data.is_active ? 'active' : 'inactive'}`,
      });
    } catch (error: any) {
      console.error("Error toggling domain status:", error);
      toast({
        title: "Error updating domain status",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  // Handle edit dialog
  const handleEdit = (domain: Domain) => {
    setSelectedDomain(domain);
    setFormData({
      name: domain.name,
      slug: domain.slug,
      description: domain.description || "",
      difficulty_level: domain.difficulty_level,
      estimated_duration_weeks: domain.estimated_duration_weeks,
      prerequisites: domain.prerequisites,
      is_active: domain.is_active,
      sort_order: domain.sort_order,
    });
    setPrerequisitesInput(domain.prerequisites.join(', '));
    setShowEditDialog(true);
  };

  // Handle delete dialog
  const handleDelete = (domain: Domain) => {
    setSelectedDomain(domain);
    setShowDeleteDialog(true);
  };

  useEffect(() => {
    fetchDomains();
  }, []);

  // Filter domains based on search term
  const filteredDomains = domains.filter(domain =>
    domain.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (domain.description && domain.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    domain.slug.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex min-h-screen cyber-grid-bg">
      <DesktopSideNav />
      
      <div className="flex flex-col flex-1 pb-16 w-full md:max-w-[calc(100%-16rem)]">
        <MobileHeader title="Admin - Domains" />
        
        <header className="hidden md:block p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigate("/admin")}
                className="text-muted-foreground"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back to Admin
              </Button>
              <h1 className="text-lg font-medium">Domain Management</h1>
            </div>
          </div>
        </header>

        <div className="p-4">
          <Tabs defaultValue="domains" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="domains">Domain Management</TabsTrigger>
              <TabsTrigger value="assignments">Topic Assignments</TabsTrigger>
            </TabsList>

            <TabsContent value="domains" className="space-y-4">
              {/* Stats Overview */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mb-4">
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-cyber-primary/10 flex items-center justify-center mr-3 shrink-0">
                  <BookOpen className="h-5 w-5 text-cyber-primary" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-muted-foreground">Total Domains</p>
                  <p className="text-lg font-bold">{domains.length}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3 shrink-0">
                  <BookOpen className="h-5 w-5 text-green-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-muted-foreground">Active Domains</p>
                  <p className="text-lg font-bold">{domains.filter(d => d.is_active).length}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3 sm:col-span-2 lg:col-span-1">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-orange-100 flex items-center justify-center mr-3 shrink-0">
                  <Clock className="h-5 w-5 text-orange-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-muted-foreground">Inactive Domains</p>
                  <p className="text-lg font-bold">{domains.filter(d => !d.is_active).length}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3 sm:col-span-2 lg:col-span-1">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3 shrink-0">
                  <BookOpen className="h-5 w-5 text-blue-600" />
                </div>
                <div className="min-w-0">
                  <p className="text-xs text-muted-foreground">Avg Duration</p>
                  <p className="text-lg font-bold">
                    {domains.length > 0
                      ? Math.round(domains.reduce((acc, d) => acc + d.estimated_duration_weeks, 0) / domains.length)
                      : 0}w
                  </p>
                </div>
              </div>
            </Card>
          </div>

          {/* Search and Actions */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search domains..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setSearchTerm("")}
                className="shrink-0"
              >
                <FilterX className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={fetchDomains}
                disabled={loading}
                className="shrink-0"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
              <Button
                onClick={() => {
                  resetForm();
                  setShowCreateDialog(true);
                }}
                className="bg-cyber-primary hover:bg-cyber-primary/90 shrink-0"
              >
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Domain</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </div>
          </div>

          {/* Domains Table */}
          <Card className="cyber-card overflow-hidden">
            {loading ? (
              <div className="p-4 text-center">
                <Loader2 className="h-8 w-8 text-cyber-primary animate-spin mx-auto mb-2" />
                <p>Loading domains...</p>
              </div>
            ) : (
              <>
                {/* Desktop Table View */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b bg-muted/50">
                        <th className="text-left font-semibold text-sm p-4 w-[30%]">Domain</th>
                        <th className="text-left font-semibold text-sm p-4 w-[35%]">Details</th>
                        <th className="text-left font-semibold text-sm p-4 w-[15%]">Status</th>
                        <th className="text-left font-semibold text-sm p-4 w-[20%]">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {filteredDomains.length > 0 ? (
                        filteredDomains.map((domain) => (
                          <tr key={domain.id} className="hover:bg-muted/20 transition-colors">
                            <td className="p-4 align-top">
                              <div className="flex items-start gap-3">
                                <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-cyber-primary/10 to-cyber-primary/20 flex items-center justify-center shrink-0 mt-0.5">
                                  {domain.icon ? (
                                    <span className="text-lg">{domain.icon}</span>
                                  ) : (
                                    <BookOpen className="h-5 w-5 text-cyber-primary" />
                                  )}
                                </div>
                                <div className="min-w-0 flex-1">
                                  <h3 className="font-semibold text-base text-foreground leading-tight mb-1">
                                    {domain.name}
                                  </h3>
                                  <p className="text-sm text-muted-foreground font-mono">
                                    /{domain.slug}
                                  </p>
                                </div>
                              </div>
                            </td>
                            <td className="p-4 align-top">
                              <div className="space-y-3">
                                <div>
                                  <p className="text-sm text-foreground leading-relaxed">
                                    {domain.description ? (
                                      domain.description.length > 120 ?
                                        `${domain.description.substring(0, 120)}...` :
                                        domain.description
                                    ) : (
                                      <span className="text-muted-foreground italic">No description provided</span>
                                    )}
                                  </p>
                                </div>
                                <div className="flex items-center gap-2 flex-wrap">
                                  <Badge
                                    variant="outline"
                                    className={`text-xs font-medium ${
                                      domain.difficulty_level === 'beginner' ? 'border-green-200 text-green-700 bg-green-50' :
                                      domain.difficulty_level === 'intermediate' ? 'border-blue-200 text-blue-700 bg-blue-50' :
                                      domain.difficulty_level === 'advanced' ? 'border-orange-200 text-orange-700 bg-orange-50' :
                                      'border-red-200 text-red-700 bg-red-50'
                                    }`}
                                  >
                                    {domain.difficulty_level.charAt(0).toUpperCase() + domain.difficulty_level.slice(1)}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs font-medium border-purple-200 text-purple-700 bg-purple-50">
                                    <Clock className="h-3 w-3 mr-1" />
                                    {domain.estimated_duration_weeks} weeks
                                  </Badge>
                                  {domain.prerequisites && domain.prerequisites.length > 0 && (
                                    <Badge variant="outline" className="text-xs font-medium border-gray-200 text-gray-700 bg-gray-50">
                                      {domain.prerequisites.length} prerequisite{domain.prerequisites.length !== 1 ? 's' : ''}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </td>
                            <td className="p-4 align-top">
                              <div className="space-y-2">
                                <div>
                                  <Badge
                                    variant={domain.is_active ? "default" : "secondary"}
                                    className={`text-xs font-medium ${
                                      domain.is_active
                                        ? "bg-green-100 text-green-800 border-green-200"
                                        : "bg-gray-100 text-gray-600 border-gray-200"
                                    }`}
                                  >
                                    {domain.is_active ? "Active" : "Inactive"}
                                  </Badge>
                                </div>
                                <div className="text-xs text-muted-foreground">
                                  <span className="font-medium">Sort Order:</span> {domain.sort_order}
                                </div>
                              </div>
                            </td>
                            <td className="p-4 align-top">
                              <div className="flex flex-col gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => toggleDomainStatus(domain)}
                                  className={`text-xs font-medium justify-start ${
                                    domain.is_active
                                      ? "text-orange-700 border-orange-200 hover:bg-orange-50"
                                      : "text-green-700 border-green-200 hover:bg-green-50"
                                  }`}
                                >
                                  {domain.is_active ? "Deactivate" : "Activate"}
                                </Button>
                                <div className="flex gap-1">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEdit(domain)}
                                    className="text-xs font-medium flex-1 text-blue-700 border-blue-200 hover:bg-blue-50"
                                  >
                                    <Edit className="h-3 w-3 mr-1" />
                                    Edit
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDelete(domain)}
                                    className="text-xs font-medium text-red-700 border-red-200 hover:bg-red-50 hover:text-red-800"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={4} className="p-4 text-center text-muted-foreground">
                            {searchTerm
                              ? "No domains found matching your search."
                              : "No domains found. Create your first domain."}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Tablet View */}
                <div className="hidden md:block lg:hidden">
                  <div className="space-y-3 p-4">
                    {filteredDomains.length > 0 ? (
                      filteredDomains.map((domain) => (
                        <Card key={domain.id} className="p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-start gap-4">
                            {/* Domain Icon & Name */}
                            <div className="flex items-start gap-3 flex-1 min-w-0">
                              <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-cyber-primary/10 to-cyber-primary/20 flex items-center justify-center shrink-0">
                                {domain.icon ? (
                                  <span className="text-xl">{domain.icon}</span>
                                ) : (
                                  <BookOpen className="h-6 w-6 text-cyber-primary" />
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between mb-2">
                                  <div>
                                    <h3 className="font-semibold text-lg text-foreground leading-tight">
                                      {domain.name}
                                    </h3>
                                    <p className="text-sm text-muted-foreground font-mono">
                                      /{domain.slug}
                                    </p>
                                  </div>
                                  <Badge
                                    variant={domain.is_active ? "default" : "secondary"}
                                    className={`ml-3 ${
                                      domain.is_active
                                        ? "bg-green-100 text-green-800 border-green-200"
                                        : "bg-gray-100 text-gray-600 border-gray-200"
                                    }`}
                                  >
                                    {domain.is_active ? "Active" : "Inactive"}
                                  </Badge>
                                </div>

                                {domain.description && (
                                  <p className="text-sm text-foreground mb-3 leading-relaxed">
                                    {domain.description.length > 150 ?
                                      `${domain.description.substring(0, 150)}...` :
                                      domain.description
                                    }
                                  </p>
                                )}

                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2 flex-wrap">
                                    <Badge
                                      variant="outline"
                                      className={`text-xs font-medium ${
                                        domain.difficulty_level === 'beginner' ? 'border-green-200 text-green-700 bg-green-50' :
                                        domain.difficulty_level === 'intermediate' ? 'border-blue-200 text-blue-700 bg-blue-50' :
                                        domain.difficulty_level === 'advanced' ? 'border-orange-200 text-orange-700 bg-orange-50' :
                                        'border-red-200 text-red-700 bg-red-50'
                                      }`}
                                    >
                                      {domain.difficulty_level.charAt(0).toUpperCase() + domain.difficulty_level.slice(1)}
                                    </Badge>
                                    <Badge variant="outline" className="text-xs font-medium border-purple-200 text-purple-700 bg-purple-50">
                                      <Clock className="h-3 w-3 mr-1" />
                                      {domain.estimated_duration_weeks} weeks
                                    </Badge>
                                    {domain.prerequisites && domain.prerequisites.length > 0 && (
                                      <Badge variant="outline" className="text-xs font-medium border-gray-200 text-gray-700 bg-gray-50">
                                        {domain.prerequisites.length} prerequisite{domain.prerequisites.length !== 1 ? 's' : ''}
                                      </Badge>
                                    )}
                                  </div>

                                  <div className="flex items-center gap-2 ml-4">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => toggleDomainStatus(domain)}
                                      className={`text-xs font-medium ${
                                        domain.is_active
                                          ? "text-orange-700 border-orange-200 hover:bg-orange-50"
                                          : "text-green-700 border-green-200 hover:bg-green-50"
                                      }`}
                                    >
                                      {domain.is_active ? "Deactivate" : "Activate"}
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleEdit(domain)}
                                      className="text-xs font-medium text-blue-700 border-blue-200 hover:bg-blue-50"
                                    >
                                      <Edit className="h-3 w-3 mr-1" />
                                      Edit
                                    </Button>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleDelete(domain)}
                                      className="text-xs font-medium text-red-700 border-red-200 hover:bg-red-50"
                                    >
                                      <Trash2 className="h-3 w-3" />
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </Card>
                      ))
                    ) : (
                      <div className="p-8 text-center text-muted-foreground">
                        <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-sm">
                          {searchTerm
                            ? "No domains found matching your search."
                            : "No domains found. Create your first domain."}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Mobile Card View */}
                <div className="md:hidden space-y-4 p-3">
                  {filteredDomains.length > 0 ? (
                    filteredDomains.map((domain) => (
                      <Card key={domain.id} className="p-4 space-y-4 hover:shadow-md transition-shadow">
                        {/* Header */}
                        <div className="flex items-start gap-3">
                          <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-cyber-primary/10 to-cyber-primary/20 flex items-center justify-center shrink-0">
                            {domain.icon ? (
                              <span className="text-xl">{domain.icon}</span>
                            ) : (
                              <BookOpen className="h-6 w-6 text-cyber-primary" />
                            )}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <div>
                                <h3 className="font-semibold text-base text-foreground leading-tight">
                                  {domain.name}
                                </h3>
                                <p className="text-sm text-muted-foreground font-mono">
                                  /{domain.slug}
                                </p>
                              </div>
                              <Badge
                                variant={domain.is_active ? "default" : "secondary"}
                                className={`ml-2 text-xs ${
                                  domain.is_active
                                    ? "bg-green-100 text-green-800 border-green-200"
                                    : "bg-gray-100 text-gray-600 border-gray-200"
                                }`}
                              >
                                {domain.is_active ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        {/* Description */}
                        {domain.description && (
                          <div className="bg-muted/30 rounded-lg p-3">
                            <p className="text-sm text-foreground leading-relaxed">
                              {domain.description.length > 100 ?
                                `${domain.description.substring(0, 100)}...` :
                                domain.description
                              }
                            </p>
                          </div>
                        )}

                        {/* Metadata */}
                        <div className="space-y-2">
                          <div className="flex items-center gap-2 flex-wrap">
                            <Badge
                              variant="outline"
                              className={`text-xs font-medium ${
                                domain.difficulty_level === 'beginner' ? 'border-green-200 text-green-700 bg-green-50' :
                                domain.difficulty_level === 'intermediate' ? 'border-blue-200 text-blue-700 bg-blue-50' :
                                domain.difficulty_level === 'advanced' ? 'border-orange-200 text-orange-700 bg-orange-50' :
                                'border-red-200 text-red-700 bg-red-50'
                              }`}
                            >
                              {domain.difficulty_level.charAt(0).toUpperCase() + domain.difficulty_level.slice(1)}
                            </Badge>
                            <Badge variant="outline" className="text-xs font-medium border-purple-200 text-purple-700 bg-purple-50">
                              <Clock className="h-3 w-3 mr-1" />
                              {domain.estimated_duration_weeks} weeks
                            </Badge>
                          </div>

                          {domain.prerequisites && domain.prerequisites.length > 0 && (
                            <Badge variant="outline" className="text-xs font-medium border-gray-200 text-gray-700 bg-gray-50">
                              {domain.prerequisites.length} prerequisite{domain.prerequisites.length !== 1 ? 's' : ''}
                            </Badge>
                          )}

                          <div className="text-xs text-muted-foreground">
                            <span className="font-medium">Sort Order:</span> {domain.sort_order}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-col gap-2 pt-3 border-t border-border">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleDomainStatus(domain)}
                            className={`text-sm font-medium w-full ${
                              domain.is_active
                                ? "text-orange-700 border-orange-200 hover:bg-orange-50"
                                : "text-green-700 border-green-200 hover:bg-green-50"
                            }`}
                          >
                            {domain.is_active ? "Deactivate Domain" : "Activate Domain"}
                          </Button>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(domain)}
                              className="text-sm font-medium flex-1 text-blue-700 border-blue-200 hover:bg-blue-50"
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(domain)}
                              className="text-sm font-medium text-red-700 border-red-200 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))
                  ) : (
                    <div className="p-8 text-center text-muted-foreground">
                      <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p className="text-sm">
                        {searchTerm
                          ? "No domains found matching your search."
                          : "No domains found. Create your first domain."}
                      </p>
                    </div>
                  )}
                </div>
              </>
            )}
          </Card>
            </TabsContent>

            <TabsContent value="assignments" className="space-y-4">
              <AdminTopicDomainAssignment />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <BottomNavigation />

      {/* Create Domain Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Domain</DialogTitle>
            <DialogDescription>
              Add a new cybersecurity domain to the platform
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* Domain Templates */}
            <div className="space-y-2">
              <Label>Quick Start Templates</Label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {domainTemplates.map((template, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => applyDomainTemplate(template)}
                    className="text-xs justify-start h-auto p-2"
                  >
                    <div className="text-left">
                      <div className="font-medium">{template.name}</div>
                      <div className="text-muted-foreground text-xs">
                        {template.difficulty_level} • {template.estimated_duration_weeks}w
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Domain Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => {
                    const name = e.target.value;
                    setFormData(prev => ({
                      ...prev,
                      name,
                      slug: prev.slug || generateSlug(name)
                    }));
                  }}
                  placeholder="e.g., Network Security"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="slug">URL Slug</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="e.g., network-security"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this domain covers..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="difficulty">Difficulty Level</Label>
                <Select
                  value={formData.difficulty_level}
                  onValueChange={(value: any) => setFormData(prev => ({ ...prev, difficulty_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="duration">Duration (weeks)</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  max="52"
                  value={formData.estimated_duration_weeks}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimated_duration_weeks: parseInt(e.target.value) || 1 }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="prerequisites">Prerequisites (comma-separated)</Label>
              <Input
                id="prerequisites"
                value={prerequisitesInput}
                onChange={(e) => setPrerequisitesInput(e.target.value)}
                placeholder="e.g., Basic networking, Computer fundamentals"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="active">Active</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="sort_order">Sort Order</Label>
                <Input
                  id="sort_order"
                  type="number"
                  min="0"
                  value={formData.sort_order}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
              disabled={formLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={createDomain}
              disabled={formLoading || !formData.name.trim()}
              className="bg-cyber-primary hover:bg-cyber-primary/90"
            >
              {formLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Domain
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Domain Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Domain</DialogTitle>
            <DialogDescription>
              Update domain information
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Domain Name *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="e.g., Network Security"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-slug">URL Slug</Label>
                <Input
                  id="edit-slug"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  placeholder="e.g., network-security"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe what this domain covers..."
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-difficulty">Difficulty Level</Label>
                <Select
                  value={formData.difficulty_level}
                  onValueChange={(value: any) => setFormData(prev => ({ ...prev, difficulty_level: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beginner">Beginner</SelectItem>
                    <SelectItem value="intermediate">Intermediate</SelectItem>
                    <SelectItem value="advanced">Advanced</SelectItem>
                    <SelectItem value="expert">Expert</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-duration">Duration (weeks)</Label>
                <Input
                  id="edit-duration"
                  type="number"
                  min="1"
                  max="52"
                  value={formData.estimated_duration_weeks}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimated_duration_weeks: parseInt(e.target.value) || 1 }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-prerequisites">Prerequisites (comma-separated)</Label>
              <Input
                id="edit-prerequisites"
                value={prerequisitesInput}
                onChange={(e) => setPrerequisitesInput(e.target.value)}
                placeholder="e.g., Basic networking, Computer fundamentals"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="edit-active"
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                />
                <Label htmlFor="edit-active">Active</Label>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-sort_order">Sort Order</Label>
                <Input
                  id="edit-sort_order"
                  type="number"
                  min="0"
                  value={formData.sort_order}
                  onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowEditDialog(false)}
              disabled={formLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={updateDomain}
              disabled={formLoading || !formData.name.trim()}
              className="bg-cyber-primary hover:bg-cyber-primary/90"
            >
              {formLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Domain
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Domain
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{selectedDomain?.name}"? This action cannot be undone and will remove all associated topics, questions, and user progress.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={formLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteDomain}
              disabled={formLoading}
              className="bg-destructive hover:bg-destructive/90"
            >
              {formLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Domain
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminDomainsPage;
