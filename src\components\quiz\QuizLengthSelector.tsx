/**
 * Quiz Length Selector Component
 * Hybrid approach with smart adaptive quiz length modes
 * Responsive design: Dropdown on mobile, cards on desktop
 */

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, Clock, Smartphone, Monitor } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface QuizLengthMode {
  id: 'quick' | 'standard' | 'comprehensive' | 'complete';
  label: string;
  description: string;
  getValue: (availableQuestions: number) => number;
  estimatedTime: string;
}

export const QUIZ_LENGTH_MODES: QuizLengthMode[] = [
  {
    id: 'quick',
    label: 'Quick Practice',
    description: 'Fast review session',
    getValue: (available) => Math.min(10, Math.max(5, Math.floor(available * 0.2))),
    estimatedTime: '10-15 min'
  },
  {
    id: 'standard',
    label: 'Standard Quiz',
    description: 'Balanced practice session',
    getValue: (available) => Math.min(15, Math.max(10, Math.floor(available * 0.4))),
    estimatedTime: '15-20 min'
  },
  {
    id: 'comprehensive',
    label: 'Comprehensive Review',
    description: 'Thorough topic coverage',
    getValue: (available) => Math.min(25, Math.max(15, Math.floor(available * 0.6))),
    estimatedTime: '25-35 min'
  },
  {
    id: 'complete',
    label: 'Complete Assessment',
    description: 'Full topic mastery test',
    getValue: (available) => Math.min(available, Math.max(20, Math.floor(available * 0.8))),
    estimatedTime: '30-45 min'
  }
];

// Legacy support for existing QuizLengthOption interface
export interface QuizLengthOption {
  value: number;
  label: string;
  description: string;
  estimatedTime: string;
}

export const QUIZ_LENGTH_OPTIONS: QuizLengthOption[] = [
  {
    value: 10,
    label: '10 Questions',
    description: 'Quick practice session',
    estimatedTime: '10-15 min'
  },
  {
    value: 15,
    label: '15 Questions',
    description: 'Standard practice',
    estimatedTime: '15-20 min'
  },
  {
    value: 20,
    label: '20 Questions',
    description: 'Comprehensive review',
    estimatedTime: '20-25 min'
  },
  {
    value: 25,
    label: '25 Questions',
    description: 'Extended practice',
    estimatedTime: '25-30 min'
  }
];

interface QuizLengthSelectorProps {
  availableQuestions: number;
  selectedLength?: number; // Made optional for mode-based selection
  selectedMode?: QuizLengthMode['id']; // New mode-based selection
  onLengthChange?: (length: number) => void; // Legacy callback
  onModeChange?: (mode: QuizLengthMode['id'], length: number) => void; // New mode callback
  onStartQuiz: () => void;
  loading?: boolean;
  disabled?: boolean;
  useModesOnly?: boolean; // Flag to use only modes (no legacy options)
  showQuestionPool?: boolean; // Show available question count
}

export const QuizLengthSelector: React.FC<QuizLengthSelectorProps> = ({
  availableQuestions,
  selectedLength,
  selectedMode = 'standard',
  onLengthChange,
  onModeChange,
  onStartQuiz,
  loading = false,
  disabled = false,
  useModesOnly = true,
  showQuestionPool = true
}) => {
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [validationSeverity, setValidationSeverity] = useState<'info' | 'warning' | 'error'>('info');
  const [isMobile, setIsMobile] = useState(false);

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Get current mode object
  const currentMode = QUIZ_LENGTH_MODES.find(mode => mode.id === selectedMode) || QUIZ_LENGTH_MODES[1];

  // Calculate effective quiz length based on mode
  const getEffectiveQuizLength = () => {
    if (useModesOnly) {
      return currentMode.getValue(availableQuestions);
    }
    return selectedLength ? Math.min(selectedLength, availableQuestions) : currentMode.getValue(availableQuestions);
  };

  // Validate quiz setup
  useEffect(() => {
    const effectiveLength = getEffectiveQuizLength();

    if (availableQuestions === 0) {
      setValidationMessage('No questions available for this topic.');
      setValidationSeverity('error');
      return;
    }

    if (availableQuestions < 10) {
      setValidationMessage(
        `This topic has very limited questions (${availableQuestions}). Quiz quality may be affected.`
      );
      setValidationSeverity('warning');
      return;
    }

    if (availableQuestions < 20) {
      setValidationMessage(
        `This topic has limited questions (${availableQuestions}). Consider adding more questions for better variety.`
      );
      setValidationSeverity('info');
      return;
    }

    setValidationMessage(null);
  }, [selectedMode, selectedLength, availableQuestions, useModesOnly]);

  const canStartQuiz = () => {
    return availableQuestions > 0 && !loading && !disabled;
  };

  const handleModeChange = (modeId: QuizLengthMode['id']) => {
    const mode = QUIZ_LENGTH_MODES.find(m => m.id === modeId);
    if (mode) {
      const length = mode.getValue(availableQuestions);
      onModeChange?.(modeId, length);
      onLengthChange?.(length); // Legacy support
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2 flex items-center gap-2">
          Choose Quiz Mode
          {isMobile ? <Smartphone className="h-4 w-4" /> : <Monitor className="h-4 w-4" />}
        </h3>
        {showQuestionPool && (
          <p className="text-muted-foreground text-sm mb-4">
            Available questions: <span className="font-medium text-foreground">{availableQuestions}</span>
            {availableQuestions < 20 && (
              <span className="text-amber-600 ml-2">• Limited pool</span>
            )}
          </p>
        )}
      </div>

      {/* Responsive Quiz Mode Selection */}
      {isMobile ? (
        /* Mobile: Dropdown */
        <div className="space-y-3">
          <Select value={selectedMode} onValueChange={handleModeChange} disabled={disabled}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select quiz mode" />
            </SelectTrigger>
            <SelectContent>
              {QUIZ_LENGTH_MODES.map((mode) => {
                const length = mode.getValue(availableQuestions);
                return (
                  <SelectItem key={mode.id} value={mode.id}>
                    <div className="flex items-center justify-between w-full">
                      <div>
                        <div className="font-medium">{mode.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {length} questions • {mode.estimatedTime}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>

          {/* Selected Mode Details for Mobile */}
          <Card className="p-3 bg-muted/30">
            <div className="text-sm">
              <div className="font-medium">{currentMode.label}</div>
              <div className="text-muted-foreground">{currentMode.description}</div>
              <div className="flex items-center gap-1 mt-1 text-xs">
                <Clock className="h-3 w-3" />
                <span>{getEffectiveQuizLength()} questions • {currentMode.estimatedTime}</span>
              </div>
            </div>
          </Card>
        </div>
      ) : (
        /* Desktop: Cards */
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">
          {QUIZ_LENGTH_MODES.map((mode) => {
            const isSelected = selectedMode === mode.id;
            const length = mode.getValue(availableQuestions);
            const isOptimal = availableQuestions >= length * 1.5; // Good variety

            return (
              <Card
                key={mode.id}
                className={`p-4 cursor-pointer transition-all border-2 ${
                  isSelected
                    ? 'border-cyber-primary bg-cyber-primary/5'
                    : 'border-border hover:border-cyber-primary/50'
                } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => {
                  if (!disabled) {
                    handleModeChange(mode.id);
                  }
                }}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <h4 className="font-medium text-sm">{mode.label}</h4>
                    {isSelected && (
                      <CheckCircle className="h-4 w-4 text-cyber-primary" />
                    )}
                  </div>
                  {isOptimal && (
                    <Badge variant="secondary" className="text-xs bg-green-100 text-green-700">
                      Optimal
                    </Badge>
                  )}
                </div>

                <p className="text-xs text-muted-foreground mb-3">
                  {mode.description}
                </p>

                <div className="space-y-1">
                  <div className="flex items-center gap-1 text-xs">
                    <span className="font-medium">{length} questions</span>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{mode.estimatedTime}</span>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Validation Message */}
      {validationMessage && (
        <Alert className={validationSeverity === 'error' ? 'border-destructive' : validationSeverity === 'warning' ? 'border-amber-500' : ''}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{validationMessage}</AlertDescription>
        </Alert>
      )}

      {/* Smart Recommendations */}
      {availableQuestions > 0 && (
        <Card className="p-4 bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
          <div className="flex items-start gap-3">
            <div className="flex-1">
              <h4 className="font-medium text-sm mb-1">Smart Recommendation</h4>
              <p className="text-xs text-muted-foreground">
                {availableQuestions >= 30
                  ? "Great question pool! All modes will provide good variety."
                  : availableQuestions >= 20
                  ? "Good question pool. Standard or Quick modes recommended."
                  : availableQuestions >= 10
                  ? "Limited questions. Quick mode recommended for best experience."
                  : "Very limited questions. Consider adding more for better variety."
                }
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Quiz Summary */}
      <Card className="p-4 bg-muted/50">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium">Quiz Summary</h4>
            <p className="text-sm text-muted-foreground">
              <span className="font-medium">{currentMode.label}</span> • {getEffectiveQuizLength()} questions • ~{Math.ceil(getEffectiveQuizLength() * 1.2)} minutes
            </p>
            {showQuestionPool && (
              <p className="text-xs text-muted-foreground mt-1">
                From pool of {availableQuestions} questions
              </p>
            )}
          </div>
          <Button
            onClick={onStartQuiz}
            disabled={!canStartQuiz()}
            className="bg-cyber-primary hover:bg-cyber-primary/90"
          >
            {loading ? 'Starting...' : 'Start Quiz'}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default QuizLengthSelector;