import { supabase } from '@/integrations/supabase/client';
import { checkApiEndpoint, getApiUrl, logApiHealthStatus } from '@/utils/api-health-checker';

/**
 * Activate free tier for a user with fallback to direct Supabase operations
 */
async function activateFreeTierForUser(email: string) {
  const apiUrl = getApiUrl();

  // First check if the API endpoint is available
  if (apiUrl) {
    const endpointHealth = await checkApiEndpoint('/api/subscriptions/activate-free-tier', apiUrl);
    logApiHealthStatus(endpointHealth, 'Free Tier Activation');

    if (endpointHealth.isAvailable) {
      try {
        const response = await fetch(`${apiUrl}/api/subscriptions/activate-free-tier`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email }),
          signal: AbortSignal.timeout(10000) // 10 second timeout
        });

        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            console.log('Free tier activated successfully via API for user:', email);
            return result;
          }
        }
      } catch (error) {
        console.warn('API endpoint failed, falling back to direct Supabase operations:', error);
      }
    }
  }

  // Fallback: Handle free tier activation directly via Supabase
  try {
    console.log('Activating free tier directly via Supabase for user:', email);

    // Get the user by email
    const { data: { user } } = await supabase.auth.getUser();

    if (user && user.email === email) {
      // Create or update user profile with basic subscription
      const { error: profileError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          email: user.email,
          is_subscribed: true,
          subscription_status: 'basic',
          subscription_plan: 'basic',
          subscription_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
        }, {
          onConflict: 'user_id'
        });

      if (profileError) {
        console.error('Error updating user profile:', profileError);
        return { success: false, error: profileError.message };
      }

      console.log('Free tier activated successfully via Supabase for user:', email);
      return { success: true, message: 'Free tier activated successfully' };
    } else {
      return { success: false, error: 'User not found or email mismatch' };
    }
  } catch (error) {
    console.error('Error activating free tier via Supabase:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

/**
 * Sign up a new user
 */
export async function signUp(email: string, password: string, metadata = {}) {
  try {
    // Always use Supabase's built-in email verification
    // but customize the redirect URL
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
        data: metadata
      }
    });

    if (error) throw error;

    // After successful signup, create user profile and activate free tier
    if (data && data.user) {
      try {
        // Use upsert to avoid conflicts if profile already exists
        const { error: profileError } = await supabase
          .from('user_profiles')
          .upsert({
            user_id: data.user.id,
            email: data.user.email,
            is_subscribed: false,
            subscription_status: 'free',
            subscription_expires_at: null,
          }, {
            onConflict: 'user_id'
          });

        if (profileError) {
          console.error('Error creating user profile:', profileError);
          // Continue with signup even if profile creation fails
        }
      } catch (profileError) {
        console.error('Error with user profile operation:', profileError);
        // Do not throw, allow registration to succeed
      }

      // Activate free tier for new users
      if (data.user.email) {
        try {
          const result = await activateFreeTierForUser(data.user.email);
          if (!result.success) {
            console.warn('Free tier activation failed but signup succeeded:', result.error);
          }
        } catch (freeTierError) {
          console.warn('Failed to activate free tier for new user, but signup succeeded:', freeTierError);
          // Don't fail the signup if free tier activation fails
        }
      }
    }

    // If signup was successful, we can show a custom message
    // but we'll use Supabase's built-in email system
    return { data, error: null };
  } catch (error) {
    console.error('Error signing up:', error);
    return { data: null, error };
  }
}

/**
 * Request password reset
 */
export async function resetPassword(email: string) {
  try {
    // Use Supabase's built-in reset password
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error resetting password:', error);
    return { data: null, error };
  }
}

/**
 * Sign in with magic link
 */
export async function signInWithMagicLink(email: string) {
  try {
    // Use Supabase's built-in magic link
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error sending magic link:', error);
    return { data: null, error };
  }
}

/**
 * Sign in with email and password
 */
export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // Update last login time in user profile
    if (data.user) {
      try {
        await supabase
          .from('user_profiles')
          .upsert({
            user_id: data.user.id,
            email: data.user.email,
            last_login_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'user_id'
          });
      } catch (profileError) {
        console.error('Error updating last login time:', profileError);
        // Don't fail the login if profile update fails
      }
    }

    // Activate free tier for new users or users without subscription
    if (data.user && data.user.email) {
      try {
        await activateFreeTierForUser(data.user.email);
      } catch (freeTierError) {
        console.warn('Failed to activate free tier, but login succeeded:', freeTierError);
        // Don't fail the login if free tier activation fails
      }
    }

    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
}

/**
 * Sign out
 */
export async function signOut() {
  try {
    // Use the scope parameter to clear all storage
    const { error } = await supabase.auth.signOut({
      scope: 'global' // This will clear all storage including on other tabs
    });

    if (error) {
      console.error('Error signing out:', error);

      // If there's an error with the token, try to clear storage manually
      if (error.message.includes('Invalid Refresh Token') ||
          error.message.includes('JWT expired') ||
          error.message.includes('token')) {
        try {
          // Clear all Supabase-related items from localStorage
          Object.keys(localStorage).forEach(key => {
            if (key.includes('supabase') || key.includes('secquiz-auth')) {
              localStorage.removeItem(key);
            }
          });

          console.log('Manually cleared auth storage after token error');
          return { error: null }; // Return success after manual cleanup
        } catch (storageError) {
          console.warn('Error clearing localStorage:', storageError);
        }
      }

      return { error };
    }

    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
}
