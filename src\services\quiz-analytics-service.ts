/**
 * Quiz Analytics Service
 * Tracks quiz mode usage and provides insights for optimization
 */

import { supabase } from '@/integrations/supabase/client';
import type { User } from '@supabase/supabase-js';

export interface QuizModeAnalytics {
  mode: 'quick' | 'standard' | 'comprehensive' | 'complete';
  topicId: string;
  userId?: string;
  questionsRequested: number;
  questionsCompleted: number;
  completionRate: number;
  timeSpent: number; // in seconds
  score: number;
  timestamp: string;
  deviceType: 'mobile' | 'desktop';
}

export interface ModeUsageStats {
  mode: 'quick' | 'standard' | 'comprehensive' | 'complete';
  usageCount: number;
  averageCompletionRate: number;
  averageScore: number;
  averageTimeSpent: number;
  popularTopics: string[];
}

export class QuizAnalyticsService {
  private static readonly ANALYTICS_KEY = 'quiz_mode_analytics';

  /**
   * Tracks quiz mode selection
   * @param user - Current user (null for guests)
   * @param mode - Selected quiz mode
   * @param topicId - Topic ID
   * @param questionsRequested - Number of questions requested
   * @returns Promise<boolean> - Success status
   */
  static async trackModeSelection(
    user: User | null,
    mode: 'quick' | 'standard' | 'comprehensive' | 'complete',
    topicId: string,
    questionsRequested: number
  ): Promise<boolean> {
    try {
      const deviceType = window.innerWidth < 768 ? 'mobile' : 'desktop';
      
      const analyticsData = {
        mode,
        topicId,
        userId: user?.id,
        questionsRequested,
        questionsCompleted: 0, // Will be updated when quiz completes
        completionRate: 0,
        timeSpent: 0,
        score: 0,
        timestamp: new Date().toISOString(),
        deviceType
      };

      // Store in localStorage for immediate access
      this.storeLocalAnalytics(analyticsData);

      // Store in database if user is authenticated
      if (user) {
        await this.storeServerAnalytics(analyticsData);
      }

      return true;
    } catch (error) {
      console.error('Error tracking mode selection:', error);
      return false;
    }
  }

  /**
   * Updates quiz completion analytics
   * @param user - Current user (null for guests)
   * @param topicId - Topic ID
   * @param questionsCompleted - Number of questions completed
   * @param score - Quiz score (0-100)
   * @param timeSpent - Time spent in seconds
   * @returns Promise<boolean> - Success status
   */
  static async trackQuizCompletion(
    user: User | null,
    topicId: string,
    questionsCompleted: number,
    score: number,
    timeSpent: number
  ): Promise<boolean> {
    try {
      // Update the most recent analytics entry for this topic
      const localData = this.getLocalAnalytics();
      const recentEntry = localData
        .filter(entry => entry.topicId === topicId && entry.userId === user?.id)
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];

      if (recentEntry) {
        recentEntry.questionsCompleted = questionsCompleted;
        recentEntry.completionRate = (questionsCompleted / recentEntry.questionsRequested) * 100;
        recentEntry.score = score;
        recentEntry.timeSpent = timeSpent;

        this.updateLocalAnalytics(localData);

        // Update server data if user is authenticated
        if (user) {
          await this.updateServerAnalytics(recentEntry);
        }
      }

      return true;
    } catch (error) {
      console.error('Error tracking quiz completion:', error);
      return false;
    }
  }

  /**
   * Gets mode usage statistics
   * @param user - Current user (null for guests)
   * @param timeRange - Time range in days (default: 30)
   * @returns Promise<ModeUsageStats[]> - Usage statistics by mode
   */
  static async getModeUsageStats(user: User | null, timeRange: number = 30): Promise<ModeUsageStats[]> {
    try {
      let analyticsData: QuizModeAnalytics[] = [];

      if (user) {
        // Get server data for authenticated users
        analyticsData = await this.getServerAnalytics(user, timeRange);
      } else {
        // Get local data for guests
        analyticsData = this.getLocalAnalytics();
      }

      // Process data to generate statistics
      const modeStats: Record<string, ModeUsageStats> = {};

      analyticsData.forEach(entry => {
        if (!modeStats[entry.mode]) {
          modeStats[entry.mode] = {
            mode: entry.mode,
            usageCount: 0,
            averageCompletionRate: 0,
            averageScore: 0,
            averageTimeSpent: 0,
            popularTopics: []
          };
        }

        const stats = modeStats[entry.mode];
        stats.usageCount++;
        stats.averageCompletionRate += entry.completionRate;
        stats.averageScore += entry.score;
        stats.averageTimeSpent += entry.timeSpent;
      });

      // Calculate averages and popular topics
      Object.values(modeStats).forEach(stats => {
        if (stats.usageCount > 0) {
          stats.averageCompletionRate /= stats.usageCount;
          stats.averageScore /= stats.usageCount;
          stats.averageTimeSpent /= stats.usageCount;

          // Get popular topics for this mode
          const topicCounts: Record<string, number> = {};
          analyticsData
            .filter(entry => entry.mode === stats.mode)
            .forEach(entry => {
              topicCounts[entry.topicId] = (topicCounts[entry.topicId] || 0) + 1;
            });

          stats.popularTopics = Object.entries(topicCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 5)
            .map(([topicId]) => topicId);
        }
      });

      return Object.values(modeStats);
    } catch (error) {
      console.error('Error getting mode usage stats:', error);
      return [];
    }
  }

  /**
   * Gets device-specific usage patterns
   * @param user - Current user (null for guests)
   * @returns Promise<object> - Device usage patterns
   */
  static async getDeviceUsagePatterns(user: User | null): Promise<{
    mobile: { preferredModes: string[]; averageSessionTime: number };
    desktop: { preferredModes: string[]; averageSessionTime: number };
  }> {
    try {
      let analyticsData: QuizModeAnalytics[] = [];

      if (user) {
        analyticsData = await this.getServerAnalytics(user, 30);
      } else {
        analyticsData = this.getLocalAnalytics();
      }

      const mobileData = analyticsData.filter(entry => entry.deviceType === 'mobile');
      const desktopData = analyticsData.filter(entry => entry.deviceType === 'desktop');

      const getModePreferences = (data: QuizModeAnalytics[]) => {
        const modeCounts: Record<string, number> = {};
        data.forEach(entry => {
          modeCounts[entry.mode] = (modeCounts[entry.mode] || 0) + 1;
        });
        return Object.entries(modeCounts)
          .sort(([, a], [, b]) => b - a)
          .map(([mode]) => mode);
      };

      const getAverageSessionTime = (data: QuizModeAnalytics[]) => {
        if (data.length === 0) return 0;
        return data.reduce((sum, entry) => sum + entry.timeSpent, 0) / data.length;
      };

      return {
        mobile: {
          preferredModes: getModePreferences(mobileData),
          averageSessionTime: getAverageSessionTime(mobileData)
        },
        desktop: {
          preferredModes: getModePreferences(desktopData),
          averageSessionTime: getAverageSessionTime(desktopData)
        }
      };
    } catch (error) {
      console.error('Error getting device usage patterns:', error);
      return {
        mobile: { preferredModes: [], averageSessionTime: 0 },
        desktop: { preferredModes: [], averageSessionTime: 0 }
      };
    }
  }

  /**
   * Stores analytics data locally
   * @param data - Analytics data to store
   */
  private static storeLocalAnalytics(data: QuizModeAnalytics): void {
    try {
      const existing = this.getLocalAnalytics();
      existing.push(data);
      
      // Keep only last 100 entries to prevent localStorage bloat
      const trimmed = existing.slice(-100);
      
      localStorage.setItem(this.ANALYTICS_KEY, JSON.stringify(trimmed));
    } catch (error) {
      console.error('Error storing local analytics:', error);
    }
  }

  /**
   * Gets analytics data from localStorage
   * @returns QuizModeAnalytics[] - Local analytics data
   */
  private static getLocalAnalytics(): QuizModeAnalytics[] {
    try {
      const stored = localStorage.getItem(this.ANALYTICS_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error getting local analytics:', error);
      return [];
    }
  }

  /**
   * Updates local analytics data
   * @param data - Updated analytics data
   */
  private static updateLocalAnalytics(data: QuizModeAnalytics[]): void {
    try {
      localStorage.setItem(this.ANALYTICS_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error updating local analytics:', error);
    }
  }

  /**
   * Stores analytics data on server
   * @param data - Analytics data to store
   */
  private static async storeServerAnalytics(data: QuizModeAnalytics): Promise<void> {
    try {
      await supabase.from('quiz_analytics').insert([data]);
    } catch (error) {
      console.error('Error storing server analytics:', error);
    }
  }

  /**
   * Updates analytics data on server
   * @param data - Analytics data to update
   */
  private static async updateServerAnalytics(data: QuizModeAnalytics): Promise<void> {
    try {
      await supabase
        .from('quiz_analytics')
        .update({
          questionsCompleted: data.questionsCompleted,
          completionRate: data.completionRate,
          score: data.score,
          timeSpent: data.timeSpent
        })
        .eq('userId', data.userId)
        .eq('topicId', data.topicId)
        .eq('timestamp', data.timestamp);
    } catch (error) {
      console.error('Error updating server analytics:', error);
    }
  }

  /**
   * Gets analytics data from server
   * @param user - Current user
   * @param timeRange - Time range in days
   * @returns Promise<QuizModeAnalytics[]> - Server analytics data
   */
  private static async getServerAnalytics(user: User, timeRange: number): Promise<QuizModeAnalytics[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - timeRange);

      const { data, error } = await supabase
        .from('quiz_analytics')
        .select('*')
        .eq('userId', user.id)
        .gte('timestamp', cutoffDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting server analytics:', error);
      return [];
    }
  }
}

export default QuizAnalyticsService;
