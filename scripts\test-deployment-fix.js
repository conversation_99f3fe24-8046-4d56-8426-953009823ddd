/**
 * Quick test to verify the deployment fix
 */

// Test the answer validation function
function testAnswerValidation() {
  console.log('🧪 Testing Answer Validation Function After Fix\n');

  // Simulate the parseCorrectAnswer function logic
  function parseCorrectAnswer(correctAnswer, optionsCount = 4) {
    const result = {
      correctIndex: 0,
      isValid: false,
      originalValue: correctAnswer
    };

    try {
      // Handle null or undefined
      if (correctAnswer === null || correctAnswer === undefined) {
        result.errorMessage = 'Correct answer is null or undefined';
        return result;
      }

      // Handle string type (prioritized for standardized format)
      if (typeof correctAnswer === 'string') {
        const trimmed = correctAnswer.trim().toUpperCase();
        
        // Handle empty string
        if (trimmed === '') {
          result.errorMessage = 'Correct answer is empty string';
          return result;
        }

        // PRIORITY: Handle standardized letter format (A, B, C, D)
        if (trimmed === 'A') {
          result.correctIndex = 0;
          result.isValid = true;
          return result;
        }
        if (trimmed === 'B') {
          result.correctIndex = 1;
          result.isValid = true;
          return result;
        }
        if (trimmed === 'C') {
          result.correctIndex = 2;
          result.isValid = true;
          return result;
        }
        if (trimmed === 'D') {
          result.correctIndex = 3;
          result.isValid = true;
          return result;
        }

        // FALLBACK: Handle legacy numeric string format (0, 1, 2, 3)
        const parsed = parseInt(trimmed, 10);
        if (!isNaN(parsed) && parsed >= 0 && parsed < optionsCount) {
          result.correctIndex = parsed;
          result.isValid = true;
          return result;
        }

        // Invalid string format
        result.errorMessage = `Invalid answer format "${trimmed}". Expected A, B, C, D or 0, 1, 2, 3`;
        return result;
      }

      // Handle number type (legacy support)
      if (typeof correctAnswer === 'number') {
        if (isNaN(correctAnswer) || !isFinite(correctAnswer)) {
          result.errorMessage = 'Correct answer is NaN or infinite';
          return result;
        }
        
        const index = Math.floor(correctAnswer);
        if (index >= 0 && index < optionsCount) {
          result.correctIndex = index;
          result.isValid = true;
          return result;
        } else {
          result.errorMessage = `Correct answer index ${index} is out of range (0-${optionsCount - 1})`;
          result.correctIndex = Math.max(0, Math.min(optionsCount - 1, index));
          return result;
        }
      }

      // Handle boolean type (treat as 0 or 1)
      if (typeof correctAnswer === 'boolean') {
        const index = correctAnswer ? 1 : 0;
        if (index < optionsCount) {
          result.correctIndex = index;
          result.isValid = true;
          return result;
        } else {
          result.errorMessage = `Boolean value ${correctAnswer} converted to index ${index} exceeds options count`;
          return result;
        }
      }

      // Handle other types
      result.errorMessage = `Unsupported correct answer type: ${typeof correctAnswer}`;
      return result;

    } catch (error) {
      result.errorMessage = `Error parsing correct answer: ${error.message}`;
      return result;
    }
  }

  // Test cases
  const testCases = [
    { input: 'A', expected: { correctIndex: 0, isValid: true } },
    { input: 'B', expected: { correctIndex: 1, isValid: true } },
    { input: 'C', expected: { correctIndex: 2, isValid: true } },
    { input: 'D', expected: { correctIndex: 3, isValid: true } },
    { input: '0', expected: { correctIndex: 0, isValid: true } },
    { input: '1', expected: { correctIndex: 1, isValid: true } },
    { input: 0, expected: { correctIndex: 0, isValid: true } },
    { input: 1, expected: { correctIndex: 1, isValid: true } },
    { input: true, expected: { correctIndex: 1, isValid: true } },
    { input: false, expected: { correctIndex: 0, isValid: true } },
  ];

  let passed = 0;
  let failed = 0;

  testCases.forEach((testCase, index) => {
    try {
      const result = parseCorrectAnswer(testCase.input);
      const success = result.correctIndex === testCase.expected.correctIndex && 
                     result.isValid === testCase.expected.isValid;
      
      if (success) {
        console.log(`✅ Test ${index + 1}: Input "${testCase.input}" → Index ${result.correctIndex}`);
        passed++;
      } else {
        console.log(`❌ Test ${index + 1}: Input "${testCase.input}" → Expected ${testCase.expected.correctIndex}, Got ${result.correctIndex}`);
        failed++;
      }
    } catch (error) {
      console.log(`💥 Test ${index + 1}: Input "${testCase.input}" → Error: ${error.message}`);
      failed++;
    }
  });

  console.log(`\n📊 Test Results:`);
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Deployment fix successful.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the implementation.');
  }

  return { passed, failed };
}

// Run the test
console.log('🚀 Testing Deployment Fix for Answer Validation\n');
testAnswerValidation();
